# Hướng dẫn sử dụng Custom Post Type "Ngành học"

## Tổng quan

Custom Post Type "Ngành học" (nganh_hoc) đã được tạo để quản lý thông tin về các ngành học, chương trình đào tạo với đầy đủ tính năng và giao diện thân thiện.

## Cấu trúc dữ liệu

### Custom Post Type: `nganh_hoc`
- **Tên hiển thị**: <PERSON><PERSON><PERSON> học
- **Slug**: nganh-hoc
- **Supports**: title, editor, thumbnail, excerpt, custom-fields
- **Menu Icon**: dashicons-welcome-learn-more
- **Menu Position**: 8

### Taxonomies

#### 1. <PERSON><PERSON><PERSON> vực (`linh_vuc`)
- **Hierarchical**: Có
- **Slug**: linh-vuc
- **<PERSON><PERSON> tả**: <PERSON><PERSON> loại ngành học theo lĩnh vực chuyên môn

**Default Terms**:
- <PERSON><PERSON><PERSON> nghệ thông tin
- Kinh tế - Quản trị
- Y tế - <PERSON><PERSON><PERSON><PERSON> phẩm
- <PERSON><PERSON> thuật - Công nghệ
- Khoa học tự nhiên
- Khoa học xã hội
- Nghệ thuật - Thiết kế
- Giáo dục - Sư phạm
- Luật - Chính trị
- Nông - Lâm - Ngư nghiệp
- Du lịch - Khách sạn
- Truyền thông - Báo chí
- Thể thao - Thể dục
- Môi trường - Tài nguyên

#### 2. Cấp độ đào tạo (`cap_do_dao_tao`)
- **Hierarchical**: Không
- **Slug**: cap-do-dao-tao
- **Mô tả**: Phân loại theo bậc học

**Default Terms**:
- Cao đẳng
- Đại học
- Thạc sĩ
- Tiến sĩ
- Chứng chỉ nghề
- Liên thông

### Custom Fields

#### Thông tin cơ bản
1. **ma_nganh_hoc** (text, required)
   - Mã định danh duy nhất
   - Format: 2-10 ký tự, chữ hoa và số
   - Ví dụ: CNTT01, KT02

2. **thoi_gian_dao_tao** (text, required)
   - Thời gian đào tạo tiêu chuẩn
   - Format: "X năm" hoặc "X.Y năm"
   - Ví dụ: "4 năm", "2.5 năm"

3. **bang_cap** (select, required)
   - Loại bằng cấp được cấp
   - Options: Cử nhân, Kỹ sư, Bác sĩ, Dược sĩ, Thạc sĩ, Tiến sĩ, Cao đẳng, Chứng chỉ

4. **hoc_phi_uoc_tinh** (text)
   - Mức học phí ước tính
   - Ví dụ: "15-20 triệu VNĐ/năm"

#### Thông tin chi tiết
5. **mon_hoc_chinh** (textarea)
   - Danh sách môn học chính
   - Mỗi môn một dòng

6. **mo_ta_chi_tiet** (wp_editor)
   - Mô tả chi tiết về ngành học
   - Hỗ trợ HTML và media

7. **dieu_kien_dau_vao** (wp_editor)
   - Các yêu cầu để được học ngành
   - Hỗ trợ HTML và media

8. **co_hoi_nghe_nghiep** (wp_editor)
   - Cơ hội việc làm và phát triển
   - Hỗ trợ HTML và media

## Cách sử dụng

### 1. Thêm ngành học mới

1. Vào **Admin Dashboard** → **Ngành học** → **Thêm ngành học mới**
2. Điền thông tin cơ bản:
   - Tiêu đề ngành học
   - Nội dung mô tả tổng quan
   - Ảnh đại diện (Featured Image)
   - Tóm tắt (Excerpt)

3. Điền thông tin chi tiết trong meta box:
   - **Mã ngành học**: Bắt buộc, định dạng chữ hoa và số
   - **Thời gian đào tạo**: Bắt buộc
   - **Bằng cấp**: Chọn từ dropdown
   - **Học phí ước tính**: Tùy chọn
   - **Môn học chính**: Liệt kê các môn, mỗi môn một dòng
   - **Mô tả chi tiết**: Sử dụng editor với HTML
   - **Điều kiện đầu vào**: Yêu cầu tuyển sinh
   - **Cơ hội nghề nghiệp**: Triển vọng việc làm

4. Chọn taxonomies:
   - **Lĩnh vực**: Chọn một hoặc nhiều lĩnh vực
   - **Cấp độ đào tạo**: Chọn cấp độ phù hợp

5. **Xuất bản** hoặc **Lưu nháp**

### 2. Quản lý danh sách ngành học

#### Giao diện Admin List
- **Cột hiển thị**: Tiêu đề, Mã ngành, Lĩnh vực, Cấp độ, Thời gian, Bằng cấp, Ngày
- **Sắp xếp**: Click vào header cột để sắp xếp
- **Lọc**: Sử dụng dropdown filters:
  - Lọc theo Lĩnh vực
  - Lọc theo Cấp độ đào tạo
  - Lọc theo Bằng cấp

#### Bulk Actions
- Cập nhật hàng loạt lĩnh vực
- Cập nhật hàng loạt cấp độ
- Xóa hàng loạt

#### Quick Edit
- Chỉnh sửa nhanh mã ngành và thời gian đào tạo
- Cập nhật taxonomies

### 3. Quản lý Taxonomies

#### Lĩnh vực
- Vào **Ngành học** → **Lĩnh vực**
- Thêm/sửa/xóa lĩnh vực
- Hỗ trợ hierarchical (lĩnh vực con)
- Thêm mô tả cho mỗi lĩnh vực

#### Cấp độ đào tạo
- Vào **Ngành học** → **Cấp độ đào tạo**
- Quản lý các cấp độ đào tạo
- Thêm mô tả cho mỗi cấp độ

## Templates Frontend

### 1. Archive Page (`archive-nganh_hoc.php`)
- URL: `/nganh-hoc/`
- Hiển thị grid các ngành học
- Bộ lọc theo lĩnh vực, cấp độ, tìm kiếm
- Pagination
- Responsive design

### 2. Single Page (`single-nganh_hoc.php`)
- URL: `/nganh-hoc/ten-nganh-hoc/`
- Hiển thị đầy đủ thông tin ngành học
- Sidebar với thông tin nhanh
- Related posts
- Social sharing
- Print functionality

### 3. Taxonomy Archives
- **Lĩnh vực**: `/linh-vuc/ten-linh-vuc/`
- **Cấp độ**: `/cap-do-dao-tao/ten-cap-do/`
- Thống kê và thông tin taxonomy
- Lọc ngành học trong taxonomy
- Sub-categories (cho lĩnh vực)

## Styling và Assets

### CSS Files
1. **nganh-hoc.css**: Styles cho frontend
   - Archive page styling
   - Single page layout
   - Card components
   - Responsive design
   - Filter interface

2. **admin-nganh-hoc.css**: Styles cho admin
   - Meta box styling
   - List table enhancements
   - Form validation styles
   - Dashboard widget

### JavaScript Files
1. **nganh-hoc.js**: Frontend functionality
   - Filter và search
   - Load more posts
   - Animations
   - Social sharing

2. **admin-nganh-hoc.js**: Admin functionality
   - Form validation
   - Character counters
   - Bulk actions
   - Auto-save

## Tính năng nâng cao

### 1. Dashboard Widget
- Thống kê tổng quan
- Số lượng ngành học published/draft
- Số lượng lĩnh vực và cấp độ
- Quick actions

### 2. Admin Enhancements
- Real-time validation
- Character counters
- Conditional fields
- Auto-save functionality
- Bulk edit modal

### 3. Frontend Features
- AJAX filtering
- Infinite scroll/Load more
- Social sharing
- Print functionality
- SEO-friendly URLs

### 4. Search Integration
- Tích hợp với WordPress search
- Custom search trong archive
- Filter combinations

## SEO và Performance

### URL Structure
- Archive: `/nganh-hoc/`
- Single: `/nganh-hoc/ten-nganh/`
- Taxonomy: `/linh-vuc/ten-linh-vuc/`
- No front base for clean URLs

### Meta Tags
- Automatic title generation
- Meta descriptions từ excerpt
- Open Graph tags
- Schema.org markup

### Performance
- Conditional asset loading
- Optimized queries
- Caching-friendly
- Lazy loading images

## Customization

### Thêm Custom Fields
1. Thêm field vào meta box callback
2. Cập nhật save function
3. Hiển thị trong templates

### Thêm Taxonomy
1. Register taxonomy trong functions.php
2. Tạo default terms
3. Thêm vào admin filters
4. Tạo taxonomy template

### Styling Customization
- Override CSS trong child theme
- Sử dụng CSS custom properties
- Responsive breakpoints
- Color scheme variables

## Troubleshooting

### Common Issues
1. **Meta box không hiển thị**: Kiểm tra user capabilities
2. **Custom fields không lưu**: Verify nonce và permissions
3. **Templates không load**: Check template hierarchy
4. **Styles không apply**: Ensure proper enqueue

### Debug Mode
- Enable WP_DEBUG
- Check error logs
- Use browser dev tools
- Validate HTML/CSS

## Best Practices

### Content Management
- Sử dụng mã ngành học duy nhất
- Điền đầy đủ thông tin bắt buộc
- Tối ưu hóa ảnh đại diện
- Viết mô tả SEO-friendly

### Performance
- Optimize images
- Use caching plugins
- Minimize HTTP requests
- Enable GZIP compression

### Security
- Validate và sanitize input
- Use nonces for forms
- Check user permissions
- Regular updates

## Support và Maintenance

### Regular Tasks
- Backup database
- Update content
- Check broken links
- Monitor performance

### Updates
- Test in staging environment
- Backup before updates
- Check compatibility
- Update documentation

---

**Phiên bản**: 1.0.0  
**Ngày tạo**: 2025-01-26  
**Tác giả**: IntHub Development Team
