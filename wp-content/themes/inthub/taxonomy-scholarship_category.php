<?php
/**
 * Taxonomy archive template for Scholarship Category
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

// Get current term
$current_term = get_queried_object();
$term_name = $current_term->name;
$term_description = $current_term->description;
$term_count = $current_term->count;
?>

<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-[#fa3c80] to-[#004aad] py-16 bg-gradient">
        <div class="container mx-auto px-4">
            <div class="text-center text-white">
                <!-- Breadcrumb -->
                <nav class="mb-6">
                    <ol class="flex justify-center items-center space-x-2 text-sm opacity-90">
                        <li><a href="<?php echo home_url(); ?>" class="hover:text-white/80"><?php _e('Trang chủ', 'inthub'); ?></a></li>
                        <li><span class="mx-2">/</span></li>
                        <li><a href="<?php echo get_post_type_archive_link('scholarship'); ?>" class="hover:text-white/80"><?php _e('Học Bổng', 'inthub'); ?></a></li>
                        <li><span class="mx-2">/</span></li>
                        <li class="text-white/80"><?php echo esc_html($term_name); ?></li>
                    </ol>
                </nav>
                
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    <?php echo esc_html($term_name); ?>
                </h1>
                
                <?php if ($term_description) : ?>
                    <p class="text-xl opacity-90 max-w-2xl mx-auto mb-4">
                        <?php echo esc_html($term_description); ?>
                    </p>
                <?php endif; ?>
                
                <div class="text-lg opacity-80">
                    <?php printf(_n('%d học bổng', '%d học bổng', $term_count, 'inthub'), $term_count); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-8 bg-white border-b">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap gap-4 items-center justify-between">
                <div class="flex flex-wrap gap-4">
                    <div class="filter-group">
                        <label for="university-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Trường đại học:', 'inthub'); ?>
                        </label>
                        <select id="university-filter" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value=""><?php _e('Tất cả trường', 'inthub'); ?></option>
                            <?php
                            $universities = get_posts(array(
                                'post_type' => 'university',
                                'posts_per_page' => -1,
                                'post_status' => 'publish',
                                'orderby' => 'title',
                                'order' => 'ASC'
                            ));
                            foreach ($universities as $university) :
                                $university_name = get_post_meta($university->ID, 'university_name', true);
                                $display_name = $university_name ?: $university->post_title;
                                ?>
                                <option value="<?php echo esc_attr($university->ID); ?>">
                                    <?php echo esc_html($display_name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="value-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Giá trị học bổng:', 'inthub'); ?>
                        </label>
                        <select id="value-filter" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value=""><?php _e('Tất cả giá trị', 'inthub'); ?></option>
                            <option value="full"><?php _e('Toàn phần', 'inthub'); ?></option>
                            <option value="partial"><?php _e('Bán phần', 'inthub'); ?></option>
                            <option value="high"><?php _e('Giá trị cao', 'inthub'); ?></option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Tìm kiếm:', 'inthub'); ?>
                        </label>
                        <input type="text" id="search-filter" placeholder="<?php _e('Tên học bổng...', 'inthub'); ?>" 
                               class="border border-gray-300 rounded-md px-3 py-2 w-64">
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <span id="results-count"><?php echo $term_count; ?></span> 
                    <?php _e('học bổng', 'inthub'); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Scholarships Grid -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div id="scholarships-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <?php
                        $scholarship_name = get_post_meta(get_the_ID(), 'scholarship_name', true);
                        $scholarship_value = get_post_meta(get_the_ID(), 'scholarship_value', true);
                        $tuition_support = get_post_meta(get_the_ID(), 'tuition_support', true);
                        $related_university = get_post_meta(get_the_ID(), 'related_university', true);
                        
                        // Get university info
                        $university_name = '';
                        if ($related_university) {
                            $university_name = get_post_meta($related_university, 'university_name', true);
                            if (!$university_name) {
                                $university_post = get_post($related_university);
                                $university_name = $university_post ? $university_post->post_title : '';
                            }
                        }
                        
                        // Get scholarship categories
                        $scholarship_categories = get_the_terms(get_the_ID(), 'scholarship_category');
                        $category_slugs = array();
                        if ($scholarship_categories && !is_wp_error($scholarship_categories)) {
                            foreach ($scholarship_categories as $cat) {
                                $category_slugs[] = $cat->slug;
                            }
                        }
                        ?>
                        
                        <div class="scholarship-card bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1" 
                             data-university="<?php echo esc_attr($related_university); ?>" 
                             data-name="<?php echo esc_attr(strtolower($scholarship_name ?: get_the_title())); ?>"
                             data-categories="<?php echo esc_attr(implode(',', $category_slugs)); ?>"
                             data-value="<?php echo esc_attr($scholarship_value); ?>">
                            
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="relative h-48 overflow-hidden">
                                    <?php the_post_thumbnail('large', array('class' => 'w-full h-full object-cover')); ?>
                                    <div class="absolute top-4 right-4">
                                        <?php if ($scholarship_value) : ?>
                                            <span class="bg-[#fa3c80] text-white px-3 py-1 rounded-full text-sm font-medium">
                                                <?php echo esc_html($scholarship_value); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="p-6">
                                <!-- Scholarship Categories -->
                                <?php if ($scholarship_categories && !is_wp_error($scholarship_categories)) : ?>
                                    <div class="flex flex-wrap gap-1 mb-3">
                                        <?php foreach ($scholarship_categories as $category) : ?>
                                            <span class="bg-[#fa3c80]/10 text-[#fa3c80] px-2 py-1 rounded text-xs font-medium">
                                                <?php echo esc_html($category->name); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                                
                                <h3 class="text-xl font-bold text-[#004aad] mb-2">
                                    <a href="<?php the_permalink(); ?>" class="hover:text-[#fa3c80] transition-colors">
                                        <?php echo esc_html($scholarship_name ?: get_the_title()); ?>
                                    </a>
                                </h3>
                                
                                <?php if ($university_name) : ?>
                                    <div class="flex items-center text-gray-600 mb-2">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <span class="text-sm"><?php echo esc_html($university_name); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($tuition_support) : ?>
                                    <div class="flex items-center text-gray-600 mb-4">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <span class="text-sm"><?php _e('Hỗ trợ:', 'inthub'); ?> <?php echo esc_html($tuition_support); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="flex items-center justify-between">
                                    <div class="text-sm text-gray-500">
                                        <?php echo get_the_date(); ?>
                                    </div>
                                    <a href="<?php the_permalink(); ?>" 
                                       class="inline-flex items-center text-[#fa3c80] hover:text-[#004aad] font-medium text-sm transition-colors">
                                        <?php _e('Xem chi tiết', 'inthub'); ?>
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else : ?>
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-500">
                            <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="text-xl font-medium mb-2"><?php _e('Không tìm thấy học bổng nào', 'inthub'); ?></h3>
                            <p><?php printf(__('Hiện tại chưa có học bổng nào trong danh mục "%s".', 'inthub'), esc_html($term_name)); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Pagination -->
            <?php if (function_exists('wp_pagenavi')) : ?>
                <div class="mt-12">
                    <?php wp_pagenavi(); ?>
                </div>
            <?php else : ?>
                <div class="mt-12 flex justify-center">
                    <?php
                    the_posts_pagination(array(
                        'mid_size' => 2,
                        'prev_text' => __('&laquo; Trước', 'inthub'),
                        'next_text' => __('Sau &raquo;', 'inthub'),
                        'class' => 'pagination'
                    ));
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<script>
// Initialize scholarship archive functionality
jQuery(document).ready(function($) {
    if (typeof ScholarshipArchive !== 'undefined') {
        ScholarshipArchive.init();
    }
});
</script>

<?php get_footer(); ?>
