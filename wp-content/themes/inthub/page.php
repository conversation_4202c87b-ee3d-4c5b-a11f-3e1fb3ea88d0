<?php
/**
 * The template for displaying all pages
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

?>
    <main id="primary" class="site-main">

        <?php if (is_front_page()) : ?>
            <?php the_content(); ?>
            <!-- Services Section -->
            <?php include(get_template_directory() . '/template-parts/services.php'); ?>
            <!-- Major Section -->
            <?php include(get_template_directory() . '/template-parts/majors.php'); ?>
            <!-- Scholarship Section -->
            <?php include(get_template_directory() . '/template-parts/scholarship.php'); ?>
            <!-- News & Events Section -->
            <?php include(get_template_directory() . '/template-parts/event-news.php'); ?>
            <!-- Testimonials Section -->
            <?php include(get_template_directory() . '/template-parts/testimonials.php'); ?>
            <!-- Contact Section -->
            <?php include(get_template_directory() . '/template-parts/contact.php'); ?>

        <?php else : ?>

            <!-- Blog/Archive Content -->
            <?php if (have_posts()) : ?>
                <div class="container mx-auto px-4 py-16">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <?php while (have_posts()) : the_post(); ?>
                            <article id="post-<?php the_ID(); ?>" <?php post_class('card'); ?>>
                                <?php if (has_post_thumbnail()) : ?>
                                    <div class="mb-4">
                                        <a href="<?php the_permalink(); ?>">
                                            <?php the_post_thumbnail('medium', array('class' => 'w-full h-48 object-cover rounded-lg')); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>

                                <div class="entry-header mb-4">
                                    <h2 class="entry-title text-xl font-semibold text-blue-900 mb-2">
                                        <a href="<?php the_permalink(); ?>" class="hover:text-pink-500 transition duration-300">
                                            <?php the_title(); ?>
                                        </a>
                                    </h2>
                                    <div class="entry-meta text-sm text-gray-600">
                                        <time datetime="<?php echo esc_attr(get_the_date('c')); ?>">
                                            <?php echo esc_html(get_the_date()); ?>
                                        </time>
                                    </div>
                                </div>

                                <div class="entry-summary text-gray-600 mb-4">
                                    <?php the_excerpt(); ?>
                                </div>

                                <div class="entry-footer">
                                    <a href="<?php the_permalink(); ?>" class="text-pink-500 font-medium hover:text-pink-600 transition duration-300">
                                        <?php esc_html_e('Đọc thêm', 'inthub'); ?> →
                                    </a>
                                </div>
                            </article>
                        <?php endwhile; ?>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-12 text-center">
                        <?php
                        the_posts_pagination(array(
                            'mid_size' => 2,
                            'prev_text' => __('← Previous', 'inthub'),
                            'next_text' => __('Next →', 'inthub'),
                        ));
                        ?>
                    </div>
                </div>
            <?php else : ?>
                <div class="container mx-auto px-4 py-16 text-center">
                    <h1 class="text-3xl font-bold text-blue-900 mb-4">
                        <?php esc_html_e('Nothing found', 'inthub'); ?>
                    </h1>
                    <p class="text-gray-600">
                        <?php esc_html_e('It looks like nothing was found at this location. Maybe try a search?', 'inthub'); ?>
                    </p>
                    <?php get_search_form(); ?>
                </div>
            <?php endif; ?>

        <?php endif; ?>

    </main>

<?php
get_footer();
?>