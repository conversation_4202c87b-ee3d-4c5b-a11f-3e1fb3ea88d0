# Hướng Dẫn Sử Dụng University Taxonomy

## Tổng Quan

Hệ thống taxonomy mới cho custom post type "university" đã được triển khai với đầy đủ tính năng:

- **Taxonomy Name**: `university_category`
- **Hierarchical**: <PERSON><PERSON> (hỗ trợ danh mục con)
- **Public**: <PERSON><PERSON> (hiển thị trên frontend)
- **SEO Friendly**: URLs thân thiện và meta tags

## 1. C<PERSON>u <PERSON>rúc <PERSON>h Mục Mặc Định

### Theo Loại Hình
- **Đ<PERSON><PERSON> học công lập** (`dai-hoc-cong-lap`)
- **Đ<PERSON>i học tư thục** (`dai-hoc-tu-thuc`)
- **<PERSON> đẳng** (`cao-dang`)
- **Học viện chuyên ngành** (`hoc-vien-chuyen-nganh`)

### <PERSON>hu <PERSON>
- **B<PERSON><PERSON>** (`bac-my`)
- **Ch<PERSON><PERSON>** (`chau-au`)
- **<PERSON><PERSON><PERSON>** (`chau-a`)
- **Ch<PERSON><PERSON>** (`chau-uc`)

### Theo Xếp <PERSON>ng
- **Top 100** (`top-100`)
- **Top 500** (`top-500`)
- **Trường mới nổi** (`truong-moi-noi`)

## 2. Cách Sử Dụng Trong Admin

### Quản Lý Danh Mục

1. **Truy cập**: Admin → Trường Đại Học → Danh mục trường
2. **Thêm danh mục mới**:
   - Nhập tên danh mục
   - Chọn danh mục cha (nếu có)
   - Nhập mô tả
   - Slug sẽ tự động tạo

3. **Chỉnh sửa danh mục**:
   - Click vào tên danh mục để chỉnh sửa
   - Cập nhật thông tin cần thiết
   - Lưu thay đổi

### Gán Danh Mục Cho University

1. **Khi tạo/sửa university**:
   - Tìm meta box "Danh mục trường" ở sidebar
   - Check vào các danh mục phù hợp
   - Có thể chọn nhiều danh mục
   - Lưu bài viết

2. **Bulk edit**:
   - Chọn nhiều universities
   - Chọn "Bulk Actions" → "Edit"
   - Thêm/xóa danh mục hàng loạt

## 3. Hiển Thị Trên Frontend

### Single University Page

**Vị trí**: Hiển thị trong hero section
**Styling**: Tags màu xanh với background trong suốt
**Code example**:
```php
$categories = inthub_get_university_categories();
if ($categories) {
    foreach ($categories as $category) {
        echo '<span class="category-tag">' . $category->name . '</span>';
    }
}
```

### Archive University Page

**Vị trí**: 
- Filter dropdown trong phần filter
- Tags nhỏ trong mỗi university card

**Chức năng**:
- Filter theo danh mục
- Hiển thị danh mục của từng trường

### Taxonomy Archive Page

**URL**: `/danh-muc-truong/{category-slug}/`
**Template**: `taxonomy-university_category.php`
**Features**:
- Hero section với thông tin danh mục
- Breadcrumb navigation
- Filter và search
- Grid hiển thị universities
- Pagination

## 4. Helper Functions

### Lấy Danh Mục

```php
// Lấy tất cả danh mục của university
$categories = inthub_get_university_categories($post_id);

// Lấy chỉ tên danh mục
$category_names = inthub_get_university_categories($post_id, 'names');

// Lấy chỉ slug danh mục
$category_slugs = inthub_get_university_categories($post_id, 'slugs');
```

### Hiển Thị Danh Mục

```php
// Hiển thị với links
echo inthub_display_university_categories($post_id, 'custom-class', true);

// Hiển thị không có links
echo inthub_display_university_categories($post_id, 'custom-class', false);
```

### Lấy Universities Theo Danh Mục

```php
// Lấy tất cả universities trong danh mục
$universities = inthub_get_universities_by_category('dai-hoc-cong-lap');

// Với custom args
$universities = inthub_get_universities_by_category('top-100', array(
    'posts_per_page' => 10,
    'meta_key' => 'featured',
    'meta_value' => 'yes'
));
```

## 5. SEO Features

### URLs Thân Thiện
- **Archive**: `/danh-muc-truong/dai-hoc-cong-lap/`
- **Hierarchical**: `/danh-muc-truong/khu-vuc/bac-my/`

### Meta Tags
- **Title**: Tự động tạo từ tên danh mục
- **Description**: Sử dụng mô tả danh mục hoặc tự động tạo
- **Open Graph**: Đầy đủ OG tags
- **Twitter Card**: Hỗ trợ Twitter Cards
- **Canonical**: URL canonical chính xác

### Sitemap
- Tự động thêm vào XML sitemap (nếu dùng Yoast SEO)
- Hỗ trợ Google indexing

## 6. Widget

### University Categories Widget

**Vị trí**: Appearance → Widgets
**Tên**: "Danh mục trường đại học"
**Options**:
- Tiêu đề widget
- Hiển thị số lượng
- Hiển thị phân cấp

**Sử dụng**:
1. Kéo widget vào sidebar
2. Cấu hình options
3. Lưu widget

## 7. Customization

### Thay Đổi Slug

Trong `functions.php`, tìm:
```php
'rewrite' => array(
    'slug' => 'danh-muc-truong', // Thay đổi ở đây
    'with_front' => false,
    'hierarchical' => true,
),
```

### Thêm Custom Fields Cho Taxonomy

```php
// Hook vào taxonomy edit form
add_action('university_category_edit_form_fields', 'add_custom_taxonomy_fields');
add_action('edited_university_category', 'save_custom_taxonomy_fields');

function add_custom_taxonomy_fields($term) {
    $custom_field = get_term_meta($term->term_id, 'custom_field', true);
    ?>
    <tr class="form-field">
        <th scope="row"><label for="custom_field">Custom Field</label></th>
        <td>
            <input type="text" name="custom_field" id="custom_field" value="<?php echo esc_attr($custom_field); ?>">
        </td>
    </tr>
    <?php
}
```

### Custom Styling

Chỉnh sửa file: `assets/css/university-taxonomy.css`

**Category tags**:
```css
.university-categories .category-link {
    background: #your-color;
    color: #your-text-color;
}
```

**Hero section**:
```css
.hero-categories .category-tag {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}
```

## 8. JavaScript Integration

### Filter Functionality

File: `assets/js/university.js`

**Filter theo danh mục**:
```javascript
$('#category-filter').on('change', function() {
    var selectedCategory = $(this).val();
    // Filter logic here
});
```

**Data attributes**:
```html
<div class="university-card" data-categories="dai-hoc-cong-lap,top-100">
```

## 9. Performance Optimization

### Caching

```php
// Cache taxonomy queries
$categories = wp_cache_get('university_categories_all', 'university');
if (false === $categories) {
    $categories = get_terms('university_category');
    wp_cache_set('university_categories_all', $categories, 'university', HOUR_IN_SECONDS);
}
```

### Database Optimization

- Sử dụng `hide_empty => true` khi không cần danh mục trống
- Limit số lượng terms khi query
- Sử dụng `fields => 'ids'` khi chỉ cần ID

## 10. Troubleshooting

### Permalink Issues
1. Vào Settings → Permalinks
2. Click "Save Changes" để flush rewrite rules
3. Hoặc chạy: `flush_rewrite_rules()`

### Taxonomy Không Hiển Thị
1. Kiểm tra user permissions
2. Đảm bảo `show_ui => true`
3. Clear cache nếu có

### Filter Không Hoạt Động
1. Kiểm tra JavaScript console
2. Đảm bảo data attributes đúng format
3. Kiểm tra jQuery loaded

## 11. Migration và Backup

### Backup Taxonomy Data
```php
// Export taxonomy terms
$terms = get_terms(array(
    'taxonomy' => 'university_category',
    'hide_empty' => false,
));

// Save to JSON
file_put_contents('university_categories_backup.json', json_encode($terms));
```

### Import Taxonomy Data
```php
// Read from JSON
$terms = json_decode(file_get_contents('university_categories_backup.json'), true);

// Import terms
foreach ($terms as $term_data) {
    wp_insert_term($term_data['name'], 'university_category', array(
        'slug' => $term_data['slug'],
        'description' => $term_data['description'],
    ));
}
```

## 12. Future Enhancements

- [ ] Advanced filtering với AJAX
- [ ] Taxonomy images
- [ ] Custom taxonomy meta fields
- [ ] Integration với search functionality
- [ ] Analytics tracking cho category views
- [ ] Multi-language support
- [ ] Import/Export tools
- [ ] Bulk category assignment tools

## Support

Để được hỗ trợ:
1. Kiểm tra console errors
2. Verify database structure
3. Test với default theme
4. Cung cấp error logs nếu có
