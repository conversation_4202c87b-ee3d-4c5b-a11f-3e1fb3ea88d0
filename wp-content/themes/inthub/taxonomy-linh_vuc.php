<?php
/**
 * Taxonomy template for Linh Vuc (Field/Domain) taxonomy
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

$current_term = get_queried_object();
?>

<div class="container mx-auto px-4 nganh-hoc-archive taxonomy-archive">
    
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <PERSON><PERSON><PERSON> học thuộc lĩnh vực: <?php echo esc_html($current_term->name); ?>
        </h1>
        <?php if ($current_term->description): ?>
            <p class="page-description">
                <?php echo esc_html($current_term->description); ?>
            </p>
        <?php endif; ?>
        
        <!-- Breadcrumb -->
        <nav class="breadcrumb mt-4">
            <ol class="breadcrumb-list">
                <li><a href="<?php echo home_url(); ?>">Trang chủ</a></li>
                <li><a href="<?php echo get_post_type_archive_link('nganh_hoc'); ?>"><PERSON><PERSON><PERSON> học</a></li>
                <li class="current"><?php echo esc_html($current_term->name); ?></li>
            </ol>
        </nav>
    </div>

    <!-- Statistics -->
    <div class="taxonomy-stats mb-6">
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number"><?php echo $current_term->count; ?></div>
                <div class="stat-label">Ngành học</div>
            </div>
            
            <?php
            // Get related education levels
            $related_cap_do = get_terms(array(
                'taxonomy' => 'cap_do_dao_tao',
                'hide_empty' => true,
                'meta_query' => array(
                    array(
                        'key' => 'related_linh_vuc',
                        'value' => $current_term->term_id,
                        'compare' => 'LIKE'
                    )
                )
            ));
            ?>
            
            <div class="stat-item">
                <div class="stat-number"><?php echo count($related_cap_do); ?></div>
                <div class="stat-label">Cấp độ đào tạo</div>
            </div>
            
            <div class="stat-item">
                <div class="stat-number">
                    <?php
                    // Count total posts in this field
                    $posts_count = get_posts(array(
                        'post_type' => 'nganh_hoc',
                        'posts_per_page' => -1,
                        'tax_query' => array(
                            array(
                                'taxonomy' => 'linh_vuc',
                                'field' => 'term_id',
                                'terms' => $current_term->term_id,
                            ),
                        ),
                        'fields' => 'ids'
                    ));
                    echo count($posts_count);
                    ?>
                </div>
                <div class="stat-label">Chương trình</div>
            </div>
        </div>
    </div>

    <!-- Sub-categories if hierarchical -->
    <?php
    $child_terms = get_terms(array(
        'taxonomy' => 'linh_vuc',
        'parent' => $current_term->term_id,
        'hide_empty' => true,
    ));
    
    if (!empty($child_terms)): ?>
        <div class="sub-categories mb-8">
            <h3 class="text-xl font-semibold mb-4">Lĩnh vực con</h3>
            <div class="sub-categories-grid">
                <?php foreach ($child_terms as $child_term): ?>
                    <a href="<?php echo get_term_link($child_term); ?>" class="sub-category-card">
                        <h4 class="sub-category-title"><?php echo esc_html($child_term->name); ?></h4>
                        <p class="sub-category-count"><?php echo $child_term->count; ?> ngành học</p>
                        <?php if ($child_term->description): ?>
                            <p class="sub-category-desc"><?php echo esc_html($child_term->description); ?></p>
                        <?php endif; ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Filters Section -->
    <div class="nganh-hoc-filters">
        <h3><i class="fas fa-filter mr-2"></i>Lọc ngành học trong lĩnh vực này</h3>
        <form class="filter-form">
            <input type="hidden" name="linh_vuc" value="<?php echo esc_attr($current_term->slug); ?>">
            
            <div class="filter-row">
                <div class="filter-group">
                    <label for="filter-cap-do">Cấp độ đào tạo</label>
                    <select id="filter-cap-do" name="cap_do_dao_tao">
                        <option value="">Tất cả cấp độ</option>
                        <?php
                        $cap_do_terms = get_terms(array(
                            'taxonomy' => 'cap_do_dao_tao',
                            'hide_empty' => true,
                        ));
                        foreach ($cap_do_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-search">Tìm kiếm</label>
                    <input type="text" id="filter-search" name="search" placeholder="Nhập tên ngành học...">
                </div>
                
                <div class="filter-group">
                    <label for="filter-sort">Sắp xếp theo</label>
                    <select id="filter-sort" name="sort">
                        <option value="title">Tên ngành học</option>
                        <option value="date">Ngày cập nhật</option>
                        <option value="popularity">Độ phổ biến</option>
                    </select>
                </div>
            </div>
            
            <div class="filter-actions">
                <button type="submit" class="btn-filter">
                    <i class="fas fa-search mr-2"></i>Lọc
                </button>
                <button type="button" class="btn-reset">
                    <i class="fas fa-undo mr-2"></i>Đặt lại
                </button>
            </div>
        </form>
    </div>

    <!-- Results Count -->
    <div class="results-info mb-4">
        <p class="results-count text-gray-600">
            <?php
            global $wp_query;
            $total = $wp_query->found_posts;
            echo "Hiển thị {$total} ngành học trong lĩnh vực " . esc_html($current_term->name);
            ?>
        </p>
    </div>

    <!-- Posts Grid -->
    <div class="nganh-hoc-grid">
        <?php if (have_posts()) : ?>
            <?php while (have_posts()) : the_post(); ?>
                <?php
                // Get custom fields
                $ma_nganh_hoc = get_post_meta(get_the_ID(), 'ma_nganh_hoc', true);
                $thoi_gian_dao_tao = get_post_meta(get_the_ID(), 'thoi_gian_dao_tao', true);
                $bang_cap = get_post_meta(get_the_ID(), 'bang_cap', true);
                $hoc_phi_uoc_tinh = get_post_meta(get_the_ID(), 'hoc_phi_uoc_tinh', true);
                
                // Get taxonomies
                $linh_vuc_terms = get_the_terms(get_the_ID(), 'linh_vuc');
                $cap_do_terms = get_the_terms(get_the_ID(), 'cap_do_dao_tao');
                
                // Get degree display name
                $bang_cap_names = array(
                    'cu-nhan' => 'Cử nhân',
                    'ky-su' => 'Kỹ sư',
                    'bac-si' => 'Bác sĩ',
                    'duoc-si' => 'Dược sĩ',
                    'thac-si' => 'Thạc sĩ',
                    'tien-si' => 'Tiến sĩ',
                    'cao-dang' => 'Cao đẳng',
                    'chung-chi' => 'Chứng chỉ'
                );
                $bang_cap_display = isset($bang_cap_names[$bang_cap]) ? $bang_cap_names[$bang_cap] : $bang_cap;
                
                // Build CSS classes for filtering
                $card_classes = array('nganh-hoc-card');
                if ($cap_do_terms && !is_wp_error($cap_do_terms)) {
                    foreach ($cap_do_terms as $term) {
                        $card_classes[] = 'cap-do-' . $term->slug;
                    }
                }
                ?>
                
                <div class="<?php echo implode(' ', $card_classes); ?>">
                    <!-- Card Header -->
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php the_permalink(); ?>" class="text-white hover:text-gray-200">
                                <?php the_title(); ?>
                            </a>
                        </h3>
                        <div class="card-meta">
                            <?php if ($ma_nganh_hoc): ?>
                                <span><i class="fas fa-code mr-1"></i><?php echo esc_html($ma_nganh_hoc); ?></span>
                            <?php endif; ?>
                            <?php if ($thoi_gian_dao_tao): ?>
                                <span><i class="fas fa-clock mr-1"></i><?php echo esc_html($thoi_gian_dao_tao); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Card Body -->
                    <div class="card-body">
                        <!-- Featured Image -->
                        <?php if (has_post_thumbnail()): ?>
                            <div class="card-image mb-3">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium', array('class' => 'w-full h-32 object-cover rounded')); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Excerpt -->
                        <?php if (has_excerpt()): ?>
                            <div class="card-excerpt">
                                <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Details Grid -->
                        <div class="card-details">
                            <?php if ($bang_cap_display): ?>
                                <div class="detail-item">
                                    <i class="fas fa-graduation-cap icon"></i>
                                    <span class="label">Bằng cấp:</span>
                                    <span class="value"><?php echo esc_html($bang_cap_display); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($hoc_phi_uoc_tinh): ?>
                                <div class="detail-item">
                                    <i class="fas fa-dollar-sign icon"></i>
                                    <span class="label">Học phí:</span>
                                    <span class="value"><?php echo esc_html($hoc_phi_uoc_tinh); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Card Footer -->
                    <div class="card-footer">
                        <!-- Tags -->
                        <div class="card-tags">
                            <?php if ($cap_do_terms && !is_wp_error($cap_do_terms)): ?>
                                <?php foreach ($cap_do_terms as $term): ?>
                                    <a href="<?php echo get_term_link($term); ?>" class="tag cap-do">
                                        <?php echo esc_html($term->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Read More Button -->
                        <a href="<?php the_permalink(); ?>" class="btn-read-more">
                            Xem chi tiết <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
                
            <?php endwhile; ?>
        <?php else : ?>
            <div class="no-posts col-span-full text-center py-12">
                <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">Chưa có ngành học nào trong lĩnh vực này</h3>
                <p class="text-gray-500 mb-4">Hãy quay lại sau hoặc khám phá các lĩnh vực khác.</p>
                <a href="<?php echo get_post_type_archive_link('nganh_hoc'); ?>" class="btn-back-to-archive">
                    Xem tất cả ngành học
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if (have_posts()) : ?>
        <div class="pagination-wrapper mt-12">
            <?php
            $pagination = paginate_links(array(
                'total' => $wp_query->max_num_pages,
                'current' => max(1, get_query_var('paged')),
                'format' => '?paged=%#%',
                'show_all' => false,
                'end_size' => 1,
                'mid_size' => 2,
                'prev_next' => true,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Trước',
                'next_text' => 'Sau <i class="fas fa-chevron-right"></i>',
                'type' => 'array'
            ));
            
            if ($pagination) : ?>
                <nav class="pagination" aria-label="Phân trang ngành học">
                    <ul class="pagination-list">
                        <?php foreach ($pagination as $page) : ?>
                            <li class="pagination-item"><?php echo $page; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    <?php endif; ?>

</div>

<!-- Additional Styles for Taxonomy Page -->
<style>
.taxonomy-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 0.9rem;
}

.breadcrumb-list li:not(:last-child)::after {
    content: '>';
    margin-left: 0.5rem;
    opacity: 0.7;
}

.breadcrumb-list a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-list a:hover {
    color: white;
}

.breadcrumb-list .current {
    color: white;
    font-weight: 600;
}

.sub-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

.sub-category-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.sub-category-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.sub-category-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.sub-category-count {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.sub-category-desc {
    font-size: 0.85rem;
    color: #666;
    line-height: 1.4;
}

.btn-back-to-archive {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-back-to-archive:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}
</style>

<?php get_footer(); ?>
