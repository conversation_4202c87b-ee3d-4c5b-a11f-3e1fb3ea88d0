<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<div id="page" class="site">
    <a class="skip-link screen-reader-text" href="#primary"><?php esc_html_e('Skip to content', 'inthub'); ?></a>

    <!-- Header -->
    <header id="masthead" class="site-header sticky top-0 z-50 bg-white shadow-md">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <!-- Logo -->
            <div class="site-branding flex items-center">
                <?php if (has_custom_logo()) : ?>
                    <div class="site-logo">
                        <?php the_custom_logo(); ?>
                    </div>
                <?php else : ?>
                    <a href="<?php echo esc_url(home_url('/')); ?>" class="site-logo flex items-center text-decoration-none">
                        <!-- Education Icon SVG -->
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                        </svg>
                        <span class="ml-2 text-2xl font-bold text-blue-900 site-title">
                            <?php bloginfo('name'); ?>
                        </span>
                    </a>
                <?php endif; ?>

                <?php if (get_theme_mod('inthub_site_description')) : ?>
                    <p class="site-description hidden md:block ml-4 text-sm text-gray-600">
                        <?php echo esc_html(get_theme_mod('inthub_site_description')); ?>
                    </p>
                <?php endif; ?>
            </div>

            <!-- Desktop Navigation -->
            <nav id="site-navigation" class="main-navigation hidden md:flex space-x-8" role="navigation" aria-label="<?php esc_attr_e('Primary Menu', 'inthub'); ?>">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'container'      => false,
                    'items_wrap'     => '<ul id="%1$s" class="%2$s flex space-x-8 relative">%3$s</ul>',
                    'walker'         => new IntHub_Walker_Nav_Menu(),
                    'depth'          => 0, // Allow unlimited depth
                    'fallback_cb'    => 'inthub_default_menu',
                ));
                ?>
            </nav>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle md:hidden text-blue-900" aria-controls="primary-menu" aria-expanded="false" aria-label="<?php esc_attr_e('Toggle navigation', 'inthub'); ?>">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </div>

        <!-- Mobile Navigation -->
        <nav id="mobile-navigation" class="mobile-navigation md:hidden hidden bg-white border-t border-gray-200" role="navigation" aria-label="<?php esc_attr_e('Mobile Menu', 'inthub'); ?>">
            <div class="container mx-auto px-4 py-4">
                <?php
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'mobile-menu',
                    'container'      => false,
                    'items_wrap'     => '<ul id="%1$s" class="%2$s space-y-2">%3$s</ul>',
                    'walker'         => new IntHub_Walker_Nav_Menu(),
                    'depth'          => 0, // Allow unlimited depth
                    'fallback_cb'    => 'inthub_mobile_default_menu',
                ));
                ?>
            </div>
        </nav>
    </header>

    <div id="content" class="site-content">

<?php
/**
 * Default menu fallback for desktop
 */
function inthub_default_menu() {
    echo '<ul class="flex space-x-8">';
    echo '<li><a href="' . esc_url(home_url('/')) . '" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Trang chủ', 'inthub') . '</a></li>';
    echo '<li><a href="#dich-vu" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Dịch vụ', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('university')) . '" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Trường đại học', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('scholarship')) . '" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Học bổng', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('event')) . '" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Sự kiện', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Tin tức', 'inthub') . '</a></li>';
    echo '<li><a href="#lien-he" class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Liên hệ', 'inthub') . '</a></li>';
    echo '</ul>';
}

/**
 * Default menu fallback for mobile
 */
function inthub_mobile_default_menu() {
    echo '<ul class="space-y-4">';
    echo '<li><a href="' . esc_url(home_url('/')) . '" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Trang chủ', 'inthub') . '</a></li>';
    echo '<li><a href="#dich-vu" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Dịch vụ', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('university')) . '" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Trường đại học', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('scholarship')) . '" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Học bổng', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_post_type_archive_link('event')) . '" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Sự kiện', 'inthub') . '</a></li>';
    echo '<li><a href="' . esc_url(get_permalink(get_option('page_for_posts'))) . '" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Tin tức', 'inthub') . '</a></li>';
    echo '<li><a href="#lien-he" class="block py-2 text-blue-900 font-medium hover:text-pink-500 transition duration-300">' . esc_html__('Liên hệ', 'inthub') . '</a></li>';
    echo '</ul>';
}
?>
