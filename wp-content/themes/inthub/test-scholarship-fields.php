<?php
/**
 * Test file for Scholarship Custom Fields
 * 
 * This file can be used to test the custom fields functionality
 * Remove this file in production
 * 
 * Usage: Include this file in functions.php temporarily for testing
 * require_once get_template_directory() . '/test-scholarship-fields.php';
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test function to create sample scholarship with custom fields
 */
function inthub_create_test_scholarship() {
    // Only run in admin and if user is admin
    if (!is_admin() || !current_user_can('manage_options')) {
        return;
    }

    // Check if test scholarship already exists
    $existing = get_posts(array(
        'post_type' => 'scholarship',
        'meta_key' => '_test_scholarship',
        'meta_value' => 'yes',
        'posts_per_page' => 1
    ));

    if (!empty($existing)) {
        return; // Test scholarship already exists
    }

    // Create test scholarship
    $scholarship_data = array(
        'post_title' => 'Test Scholarship - Custom Fields Demo',
        'post_content' => 'This is a test scholarship created to demonstrate the new custom fields functionality. You can safely delete this after testing.',
        'post_status' => 'draft', // Keep as draft
        'post_type' => 'scholarship'
    );

    $scholarship_id = wp_insert_post($scholarship_data);

    if ($scholarship_id && !is_wp_error($scholarship_id)) {
        // Add basic scholarship meta
        update_post_meta($scholarship_id, 'scholarship_name', 'Test Merit Scholarship');
        update_post_meta($scholarship_id, 'scholarship_value', '$15,000/năm');
        update_post_meta($scholarship_id, 'tuition_support', '75% học phí');
        update_post_meta($scholarship_id, '_test_scholarship', 'yes');

        // Add sample application requirements (new structure)
        $sample_requirements = array(
            array(
                'title' => 'Thành tích học tập',
                'description' => 'GPA tối thiểu 3.5/4.0 hoặc tương đương. Cần có bảng điểm chính thức từ trường học trước đó.'
            ),
            array(
                'title' => 'Chứng chỉ tiếng Anh',
                'description' => 'IELTS 6.5+ hoặc TOEFL iBT 80+. Chứng chỉ phải còn hiệu lực trong vòng 2 năm.'
            ),
            array(
                'title' => 'Thư giới thiệu',
                'description' => '2-3 thư giới thiệu từ giáo viên hoặc giảng viên. Thư phải được viết bằng tiếng Anh và có chữ ký gốc.'
            ),
            array(
                'title' => 'Bài luận động lực',
                'description' => 'Personal Statement 500-1000 từ, trình bày rõ động lực học tập và kế hoạch nghề nghiệp.'
            ),
            array(
                'title' => 'Chứng minh tài chính',
                'description' => 'Sponsor letter hoặc bank statement chứng minh khả năng chi trả học phí và sinh hoạt phí.'
            )
        );
        update_post_meta($scholarship_id, 'application_requirements', $sample_requirements);

        // Add sample application process
        $sample_process = array(
            array(
                'title' => 'Chuẩn bị hồ sơ',
                'description' => 'Thu thập tất cả các tài liệu cần thiết bao gồm bảng điểm, chứng chỉ tiếng Anh, thư giới thiệu và các giấy tờ khác theo yêu cầu của trường.'
            ),
            array(
                'title' => 'Nộp đơn trực tuyến',
                'description' => 'Điền đầy đủ thông tin vào form đăng ký online và tải lên tất cả tài liệu đã chuẩn bị. Kiểm tra kỹ thông tin trước khi submit.'
            ),
            array(
                'title' => 'Phỏng vấn',
                'description' => 'Tham gia phỏng vấn trực tuyến hoặc trực tiếp với ban tuyển sinh. Chuẩn bị trả lời các câu hỏi về động lực học tập và kế hoạch tương lai.'
            ),
            array(
                'title' => 'Chờ kết quả',
                'description' => 'Chờ thông báo kết quả từ trường trong vòng 4-6 tuần. Nếu được chấp nhận, làm thủ tục nhập học theo hướng dẫn.'
            )
        );
        update_post_meta($scholarship_id, 'application_process', $sample_process);

        // Add admin notice
        add_action('admin_notices', function() use ($scholarship_id) {
            echo '<div class="notice notice-success is-dismissible">';
            echo '<p><strong>Test Scholarship Created!</strong> ';
            echo 'A test scholarship with custom fields has been created. ';
            echo '<a href="' . get_edit_post_link($scholarship_id) . '">Edit it here</a> ';
            echo 'or <a href="' . get_permalink($scholarship_id) . '">view on frontend</a>.</p>';
            echo '</div>';
        });
    }
}

/**
 * Test function to validate custom fields data
 */
function inthub_test_custom_fields_functions() {
    // Get all scholarships with custom fields
    $scholarships = get_posts(array(
        'post_type' => 'scholarship',
        'posts_per_page' => -1,
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => 'application_requirements',
                'compare' => 'EXISTS'
            ),
            array(
                'key' => 'application_process',
                'compare' => 'EXISTS'
            )
        )
    ));

    $test_results = array();

    foreach ($scholarships as $scholarship) {
        $scholarship_id = $scholarship->ID;
        $results = array(
            'id' => $scholarship_id,
            'title' => $scholarship->post_title,
            'requirements_count' => 0,
            'process_count' => 0,
            'functions_work' => true
        );

        // Test requirements functions
        $requirements = inthub_get_application_requirements($scholarship_id);
        if (is_array($requirements)) {
            $results['requirements_count'] = count($requirements);
            
            // Test display function
            $html = inthub_display_application_requirements($scholarship_id);
            if (empty($html)) {
                $results['functions_work'] = false;
            }
        }

        // Test process functions
        $process = inthub_get_application_process($scholarship_id);
        if (is_array($process)) {
            $results['process_count'] = count($process);
            
            // Test display function
            $html = inthub_display_application_process($scholarship_id);
            if (empty($html)) {
                $results['functions_work'] = false;
            }
        }

        $test_results[] = $results;
    }

    return $test_results;
}

/**
 * Add test menu in admin (only for admins)
 */
function inthub_add_test_menu() {
    if (!current_user_can('manage_options')) {
        return;
    }

    add_submenu_page(
        'edit.php?post_type=scholarship',
        'Test Custom Fields',
        'Test Fields',
        'manage_options',
        'test-scholarship-fields',
        'inthub_test_page_callback'
    );
}
add_action('admin_menu', 'inthub_add_test_menu');

/**
 * Test page callback
 */
function inthub_test_page_callback() {
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }

    // Handle test creation
    if (isset($_POST['create_test']) && wp_verify_nonce($_POST['test_nonce'], 'create_test_scholarship')) {
        inthub_create_test_scholarship();
        echo '<div class="notice notice-success"><p>Test scholarship creation initiated!</p></div>';
    }

    // Get test results
    $test_results = inthub_test_custom_fields_functions();

    ?>
    <div class="wrap">
        <h1>Scholarship Custom Fields Test</h1>
        
        <div class="card">
            <h2>Create Test Scholarship</h2>
            <p>Create a test scholarship with sample custom fields data.</p>
            <form method="post">
                <?php wp_nonce_field('create_test_scholarship', 'test_nonce'); ?>
                <input type="submit" name="create_test" class="button button-primary" value="Create Test Scholarship">
            </form>
        </div>

        <div class="card">
            <h2>Test Results</h2>
            <p>Testing custom fields functions on existing scholarships:</p>
            
            <?php if (empty($test_results)) : ?>
                <p><em>No scholarships with custom fields found.</em></p>
            <?php else : ?>
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Title</th>
                            <th>Requirements</th>
                            <th>Process Steps</th>
                            <th>Functions Work</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($test_results as $result) : ?>
                            <tr>
                                <td><?php echo $result['id']; ?></td>
                                <td>
                                    <a href="<?php echo get_edit_post_link($result['id']); ?>">
                                        <?php echo esc_html($result['title']); ?>
                                    </a>
                                </td>
                                <td><?php echo $result['requirements_count']; ?></td>
                                <td><?php echo $result['process_count']; ?></td>
                                <td>
                                    <?php if ($result['functions_work']) : ?>
                                        <span style="color: green;">✓ Yes</span>
                                    <?php else : ?>
                                        <span style="color: red;">✗ No</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>

        <div class="card">
            <h2>File Status</h2>
            <ul>
                <li>Admin JS: <?php echo file_exists(get_template_directory() . '/assets/js/admin-scholarship.js') ? '✓' : '✗'; ?></li>
                <li>Admin CSS: <?php echo file_exists(get_template_directory() . '/assets/css/admin-scholarship.css') ? '✓' : '✗'; ?></li>
                <li>Frontend CSS: <?php echo file_exists(get_template_directory() . '/assets/css/scholarship-frontend.css') ? '✓' : '✗'; ?></li>
                <li>Guide: <?php echo file_exists(get_template_directory() . '/SCHOLARSHIP_CUSTOM_FIELDS_GUIDE.md') ? '✓' : '✗'; ?></li>
            </ul>
        </div>
    </div>
    <?php
}

// Auto-create test scholarship on activation (only once)
add_action('admin_init', function() {
    if (get_option('inthub_test_scholarship_created') !== 'yes') {
        inthub_create_test_scholarship();
        update_option('inthub_test_scholarship_created', 'yes');
    }
});
?>
