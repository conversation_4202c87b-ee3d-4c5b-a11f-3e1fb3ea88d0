<?php
/**
 * Taxonomy template for Cap Do Dao Tao (Education Level) taxonomy
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

$current_term = get_queried_object();
?>

<div class="container mx-auto px-4 nganh-hoc-archive taxonomy-archive">
    
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <PERSON><PERSON><PERSON> học cấp độ: <?php echo esc_html($current_term->name); ?>
        </h1>
        <?php if ($current_term->description): ?>
            <p class="page-description">
                <?php echo esc_html($current_term->description); ?>
            </p>
        <?php endif; ?>
        
        <!-- Breadcrumb -->
        <nav class="breadcrumb mt-4">
            <ol class="breadcrumb-list">
                <li><a href="<?php echo home_url(); ?>">Trang chủ</a></li>
                <li><a href="<?php echo get_post_type_archive_link('nganh_hoc'); ?>"><PERSON><PERSON><PERSON> học</a></li>
                <li class="current"><?php echo esc_html($current_term->name); ?></li>
            </ol>
        </nav>
    </div>

    <!-- Education Level Info -->
    <div class="education-level-info mb-8">
        <div class="info-grid">
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-graduation-cap"></i>
                </div>
                <div class="info-content">
                    <h3>Cấp độ đào tạo</h3>
                    <p><?php echo esc_html($current_term->name); ?></p>
                </div>
            </div>
            
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="info-content">
                    <h3>Số lượng ngành học</h3>
                    <p><?php echo $current_term->count; ?> ngành</p>
                </div>
            </div>
            
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="info-content">
                    <h3>Thời gian đào tạo</h3>
                    <p>
                        <?php
                        // Get typical duration for this education level
                        $typical_durations = array(
                            'cao-dang' => '2-3 năm',
                            'dai-hoc' => '4-5 năm',
                            'thac-si' => '1.5-2 năm',
                            'tien-si' => '3-4 năm',
                            'chung-chi-nghe' => '6 tháng - 2 năm',
                            'lien-thong' => 'Tùy chương trình'
                        );
                        echo isset($typical_durations[$current_term->slug]) ? $typical_durations[$current_term->slug] : 'Tùy ngành học';
                        ?>
                    </p>
                </div>
            </div>
            
            <div class="info-card">
                <div class="info-icon">
                    <i class="fas fa-certificate"></i>
                </div>
                <div class="info-content">
                    <h3>Bằng cấp</h3>
                    <p>
                        <?php
                        // Get typical degree for this education level
                        $typical_degrees = array(
                            'cao-dang' => 'Cao đẳng',
                            'dai-hoc' => 'Cử nhân/Kỹ sư',
                            'thac-si' => 'Thạc sĩ',
                            'tien-si' => 'Tiến sĩ',
                            'chung-chi-nghe' => 'Chứng chỉ',
                            'lien-thong' => 'Tùy chương trình'
                        );
                        echo isset($typical_degrees[$current_term->slug]) ? $typical_degrees[$current_term->slug] : 'Tùy ngành học';
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Fields Statistics -->
    <?php
    $related_fields = get_terms(array(
        'taxonomy' => 'linh_vuc',
        'hide_empty' => true,
        'meta_query' => array(
            'relation' => 'OR',
            array(
                'key' => 'related_cap_do',
                'value' => $current_term->term_id,
                'compare' => 'LIKE'
            )
        )
    ));
    
    if (!empty($related_fields)): ?>
        <div class="related-fields mb-8">
            <h3 class="text-xl font-semibold mb-4">Lĩnh vực có ngành học cấp <?php echo esc_html($current_term->name); ?></h3>
            <div class="fields-grid">
                <?php foreach ($related_fields as $field): 
                    // Count posts in this field with current education level
                    $field_posts_count = get_posts(array(
                        'post_type' => 'nganh_hoc',
                        'posts_per_page' => -1,
                        'tax_query' => array(
                            'relation' => 'AND',
                            array(
                                'taxonomy' => 'linh_vuc',
                                'field' => 'term_id',
                                'terms' => $field->term_id,
                            ),
                            array(
                                'taxonomy' => 'cap_do_dao_tao',
                                'field' => 'term_id',
                                'terms' => $current_term->term_id,
                            ),
                        ),
                        'fields' => 'ids'
                    ));
                    
                    if (count($field_posts_count) > 0): ?>
                        <a href="<?php echo get_term_link($field); ?>" class="field-card">
                            <h4 class="field-title"><?php echo esc_html($field->name); ?></h4>
                            <p class="field-count"><?php echo count($field_posts_count); ?> ngành học</p>
                        </a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Filters Section -->
    <div class="nganh-hoc-filters">
        <h3><i class="fas fa-filter mr-2"></i>Lọc ngành học cấp <?php echo esc_html($current_term->name); ?></h3>
        <form class="filter-form">
            <input type="hidden" name="cap_do_dao_tao" value="<?php echo esc_attr($current_term->slug); ?>">
            
            <div class="filter-row">
                <div class="filter-group">
                    <label for="filter-linh-vuc">Lĩnh vực</label>
                    <select id="filter-linh-vuc" name="linh_vuc">
                        <option value="">Tất cả lĩnh vực</option>
                        <?php
                        $linh_vuc_terms = get_terms(array(
                            'taxonomy' => 'linh_vuc',
                            'hide_empty' => true,
                        ));
                        foreach ($linh_vuc_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-search">Tìm kiếm</label>
                    <input type="text" id="filter-search" name="search" placeholder="Nhập tên ngành học...">
                </div>
                
                <div class="filter-group">
                    <label for="filter-sort">Sắp xếp theo</label>
                    <select id="filter-sort" name="sort">
                        <option value="title">Tên ngành học</option>
                        <option value="date">Ngày cập nhật</option>
                        <option value="popularity">Độ phổ biến</option>
                    </select>
                </div>
            </div>
            
            <div class="filter-actions">
                <button type="submit" class="btn-filter">
                    <i class="fas fa-search mr-2"></i>Lọc
                </button>
                <button type="button" class="btn-reset">
                    <i class="fas fa-undo mr-2"></i>Đặt lại
                </button>
            </div>
        </form>
    </div>

    <!-- Results Count -->
    <div class="results-info mb-4">
        <p class="results-count text-gray-600">
            <?php
            global $wp_query;
            $total = $wp_query->found_posts;
            echo "Hiển thị {$total} ngành học cấp " . esc_html($current_term->name);
            ?>
        </p>
    </div>

    <!-- Posts Grid -->
    <div class="nganh-hoc-grid">
        <?php if (have_posts()) : ?>
            <?php while (have_posts()) : the_post(); ?>
                <?php
                // Get custom fields
                $ma_nganh_hoc = get_post_meta(get_the_ID(), 'ma_nganh_hoc', true);
                $thoi_gian_dao_tao = get_post_meta(get_the_ID(), 'thoi_gian_dao_tao', true);
                $bang_cap = get_post_meta(get_the_ID(), 'bang_cap', true);
                $hoc_phi_uoc_tinh = get_post_meta(get_the_ID(), 'hoc_phi_uoc_tinh', true);
                
                // Get taxonomies
                $linh_vuc_terms = get_the_terms(get_the_ID(), 'linh_vuc');
                $cap_do_terms = get_the_terms(get_the_ID(), 'cap_do_dao_tao');
                
                // Get degree display name
                $bang_cap_names = array(
                    'cu-nhan' => 'Cử nhân',
                    'ky-su' => 'Kỹ sư',
                    'bac-si' => 'Bác sĩ',
                    'duoc-si' => 'Dược sĩ',
                    'thac-si' => 'Thạc sĩ',
                    'tien-si' => 'Tiến sĩ',
                    'cao-dang' => 'Cao đẳng',
                    'chung-chi' => 'Chứng chỉ'
                );
                $bang_cap_display = isset($bang_cap_names[$bang_cap]) ? $bang_cap_names[$bang_cap] : $bang_cap;
                
                // Build CSS classes for filtering
                $card_classes = array('nganh-hoc-card');
                if ($linh_vuc_terms && !is_wp_error($linh_vuc_terms)) {
                    foreach ($linh_vuc_terms as $term) {
                        $card_classes[] = 'linh-vuc-' . $term->slug;
                    }
                }
                ?>
                
                <div class="<?php echo implode(' ', $card_classes); ?>">
                    <!-- Card Header -->
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php the_permalink(); ?>" class="text-white hover:text-gray-200">
                                <?php the_title(); ?>
                            </a>
                        </h3>
                        <div class="card-meta">
                            <?php if ($ma_nganh_hoc): ?>
                                <span><i class="fas fa-code mr-1"></i><?php echo esc_html($ma_nganh_hoc); ?></span>
                            <?php endif; ?>
                            <?php if ($thoi_gian_dao_tao): ?>
                                <span><i class="fas fa-clock mr-1"></i><?php echo esc_html($thoi_gian_dao_tao); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Card Body -->
                    <div class="card-body">
                        <!-- Featured Image -->
                        <?php if (has_post_thumbnail()): ?>
                            <div class="card-image mb-3">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium', array('class' => 'w-full h-32 object-cover rounded')); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Excerpt -->
                        <?php if (has_excerpt()): ?>
                            <div class="card-excerpt">
                                <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Details Grid -->
                        <div class="card-details">
                            <?php if ($bang_cap_display): ?>
                                <div class="detail-item">
                                    <i class="fas fa-graduation-cap icon"></i>
                                    <span class="label">Bằng cấp:</span>
                                    <span class="value"><?php echo esc_html($bang_cap_display); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($hoc_phi_uoc_tinh): ?>
                                <div class="detail-item">
                                    <i class="fas fa-dollar-sign icon"></i>
                                    <span class="label">Học phí:</span>
                                    <span class="value"><?php echo esc_html($hoc_phi_uoc_tinh); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Card Footer -->
                    <div class="card-footer">
                        <!-- Tags -->
                        <div class="card-tags">
                            <?php if ($linh_vuc_terms && !is_wp_error($linh_vuc_terms)): ?>
                                <?php foreach ($linh_vuc_terms as $term): ?>
                                    <a href="<?php echo get_term_link($term); ?>" class="tag linh-vuc">
                                        <?php echo esc_html($term->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Read More Button -->
                        <a href="<?php the_permalink(); ?>" class="btn-read-more">
                            Xem chi tiết <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
                
            <?php endwhile; ?>
        <?php else : ?>
            <div class="no-posts col-span-full text-center py-12">
                <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">Chưa có ngành học nào ở cấp độ này</h3>
                <p class="text-gray-500 mb-4">Hãy quay lại sau hoặc khám phá các cấp độ khác.</p>
                <a href="<?php echo get_post_type_archive_link('nganh_hoc'); ?>" class="btn-back-to-archive">
                    Xem tất cả ngành học
                </a>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if (have_posts()) : ?>
        <div class="pagination-wrapper mt-12">
            <?php
            $pagination = paginate_links(array(
                'total' => $wp_query->max_num_pages,
                'current' => max(1, get_query_var('paged')),
                'format' => '?paged=%#%',
                'show_all' => false,
                'end_size' => 1,
                'mid_size' => 2,
                'prev_next' => true,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Trước',
                'next_text' => 'Sau <i class="fas fa-chevron-right"></i>',
                'type' => 'array'
            ));
            
            if ($pagination) : ?>
                <nav class="pagination" aria-label="Phân trang ngành học">
                    <ul class="pagination-list">
                        <?php foreach ($pagination as $page) : ?>
                            <li class="pagination-item"><?php echo $page; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    <?php endif; ?>

</div>

<!-- Additional Styles for Education Level Page -->
<style>
.education-level-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 15px;
    padding: 2rem;
    color: white;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.info-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.info-icon {
    font-size: 2rem;
    opacity: 0.9;
}

.info-content h3 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    opacity: 0.9;
}

.info-content p {
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0;
}

.fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.field-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    text-align: center;
}

.field-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.field-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.field-count {
    font-size: 0.9rem;
    color: #4facfe;
    font-weight: 600;
    margin: 0;
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 0.9rem;
}

.breadcrumb-list li:not(:last-child)::after {
    content: '>';
    margin-left: 0.5rem;
    opacity: 0.7;
}

.breadcrumb-list a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-list a:hover {
    color: white;
}

.breadcrumb-list .current {
    color: white;
    font-weight: 600;
}

.btn-back-to-archive {
    display: inline-block;
    background: #4facfe;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-back-to-archive:hover {
    background: #3d8bfe;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-card {
        flex-direction: column;
        text-align: center;
    }
    
    .fields-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php get_footer(); ?>
