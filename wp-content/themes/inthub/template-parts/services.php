<?php
$args = array(
    'post_type' => 'services',
    'posts_per_page' => 6,
    'post_status' => 'publish',
    'order' => 'DESC',
    'orderby' => 'date'
);

$latest_posts = new WP_Query($args);
?>
<section id="dich-vu" class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-[#004aad] mb-4"><?php _e('Dịch vụ của chúng tôi', 'inthub'); ?></h2>
            <p class="text-[#004aad] opacity-70 max-w-2xl mx-auto"><?php _e('Chúng tôi cung cấp đầy đủ các dịch vụ du học chuyên nghiệp, gi<PERSON><PERSON> b<PERSON>n hiện thực hóa ước mơ du học một cách dễ dàng.', 'inthub') ?></p>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php if ($latest_posts->have_posts()) : ?>
                <?php while ($latest_posts->have_posts()) : $latest_posts->the_post(); ?>
                    <div class="card bg-white shadow-lg p-6 border-t-4 border-[#fa3c80]">
                        <div class="bg-[#fa3c80] bg-opacity-10 flex items-center justify-center mb-6">
                            <?php if (has_post_thumbnail()) : ?>

                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('full', array('class' => 'news-image w-full h-full')); ?>
                                </a>

                            <?php else : ?>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-[#fa3c80]" fill="none"
                                     viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <h3 class="text-xl font-semibold text-[#004aad] mb-3"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h3>
                        <p class="text-[#004aad] opacity-70 mb-4"><?php the_excerpt(); ?></p>
                        <a href="<?php the_permalink(); ?>" class="inline-flex items-center text-[#fa3c80] font-medium">
                            <?php _e('Tìm hiểu thêm', 'inthub'); ?>
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-1" viewBox="0 0 20 20"
                                 fill="currentColor">
                                <path fill-rule="evenodd"
                                      d="M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z"
                                      clip-rule="evenodd"></path>
                            </svg>
                        </a>
                    </div>
                <?php endwhile; ?>
                <?php wp_reset_postdata(); ?>
            <?php endif; ?>
        </div>
    </div>
</section>
