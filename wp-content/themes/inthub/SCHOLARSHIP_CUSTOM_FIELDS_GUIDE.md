# Hướng Dẫn Sử Dụng Custom Fields Học Bổng

## Tổng Quan

Hệ thống custom fields mới cho post type "scholarship" đã được triển khai với 2 tính năng chính:

1. **Y<PERSON>u cầu ứng tuyển** - Repeater field cho phép thêm nhiều yêu cầu động
2. **Quy trình ứng tuyển** - Repeater field cho phép thêm nhiều bước quy trình động

## 1. Cấu Trúc Custom Fields Mới

### Yêu cầu Ứng Tuyển (application_requirements)
- **Kiểu dữ liệu**: Array/Repeater field
- **Cấu trúc mỗi yêu cầu**:
  - `title` (string): Tiêu đề yêu cầu
  - `description` (string): Mô tả chi tiết yêu cầu
- **Validation**: Cả title và description đều bắt buộc
- **Giới hạn**: 500 ký tự mỗi mô tả
- **Sắp xếp**: <PERSON><PERSON> thể kéo thả để sắp xếp thứ tự
- **Backward Compatibility**: Tự động convert dữ liệu cũ (string) sang format mới

### Quy Trình Ứng Tuyển (application_process)
- **Kiểu dữ liệu**: Array/Repeater field
- **Cấu trúc mỗi bước**:
  - `title` (string): Tiêu đề bước
  - `description` (string): Mô tả chi tiết bước
- **Validation**: Cả title và description đều bắt buộc
- **Giới hạn**: 500 ký tự mỗi mô tả
- **Sắp xếp**: Có thể kéo thả để sắp xếp thứ tự

## 2. Cách Sử Dụng Trong Admin

### Thêm/Chỉnh Sửa Học Bổng

1. **Truy cập**: Admin → Học Bổng → Thêm mới hoặc chỉnh sửa
2. **Tìm meta box**: "Chi tiết học bổng" 
3. **Cuộn xuống**: Tìm phần "Yêu cầu ứng tuyển" và "Quy trình ứng tuyển"

### Yêu Cầu Ứng Tuyển

1. **Thêm yêu cầu mới**:
   - Nhấn nút "Thêm yêu cầu"
   - Nhập tiêu đề yêu cầu (bắt buộc)
   - Nhập mô tả chi tiết vào textarea (bắt buộc)
   - Có thể sử dụng xuống dòng và định dạng cơ bản

2. **Sắp xếp thứ tự**:
   - Kéo icon "⋮⋮" để di chuyển yêu cầu
   - Thứ tự sẽ được hiển thị theo số thứ tự trên frontend

3. **Xóa yêu cầu**:
   - Nhấn nút "Xóa" màu đỏ
   - Sẽ có xác nhận nếu đã có nội dung

### Quy Trình Ứng Tuyển

1. **Thêm bước mới**:
   - Nhấn nút "Thêm bước"
   - Nhập tiêu đề bước (bắt buộc)
   - Nhập mô tả chi tiết (bắt buộc)

2. **Sắp xếp thứ tự**:
   - Kéo icon "⋮⋮" để di chuyển bước
   - Bước cuối cùng sẽ có màu khác trên frontend

3. **Xóa bước**:
   - Nhấn nút "Xóa" màu đỏ
   - Sẽ có xác nhận nếu đã có nội dung

## 3. Hiển Thị Trên Frontend

### Yêu Cầu Ứng Tuyển

- **Vị trí**: Trong phần "Chi tiết học bổng"
- **Hiển thị**: Danh sách có số thứ tự với styling đẹp
- **Fallback**: Nếu không có custom requirements, hiển thị yêu cầu mặc định

### Quy Trình Ứng Tuyển

- **Vị trí**: Phần riêng "Quy trình ứng tuyển"
- **Hiển thị**: Timeline với số thứ tự và màu sắc
- **Bước cuối**: Có màu khác (#fa3c80) để làm nổi bật
- **Fallback**: Nếu không có custom process, hiển thị quy trình mặc định

## 4. Functions Helper

### Lấy Dữ Liệu

```php
// Lấy yêu cầu ứng tuyển
$requirements = inthub_get_application_requirements($post_id);

// Lấy quy trình ứng tuyển  
$process = inthub_get_application_process($post_id);
```

### Hiển Thị HTML

```php
// Hiển thị yêu cầu ứng tuyển
echo inthub_display_application_requirements($post_id, 'custom-class');

// Hiển thị quy trình ứng tuyển
echo inthub_display_application_process($post_id, 'custom-class');
```

## 5. Customization

### Thay Đổi Styling

1. **Admin**: Chỉnh sửa `assets/css/admin-scholarship.css`
2. **Frontend**: Chỉnh sửa `assets/css/scholarship-frontend.css`

### Thêm Validation

Chỉnh sửa `assets/js/admin-scholarship.js` để thêm rules validation mới.

### Thay Đổi Giới Hạn Ký Tự

Trong `admin-scholarship.js`, tìm dòng:
```javascript
var maxLength = 500; // Set max length
```

## 6. Database Structure

### application_requirements
```
meta_key: application_requirements
meta_value: [
  {"title": "Thành tích học tập", "description": "GPA tối thiểu 3.5/4.0 hoặc tương đương"},
  {"title": "Chứng chỉ tiếng Anh", "description": "IELTS 6.5+ hoặc TOEFL iBT 80+"}
]
```

**Backward Compatibility**: Dữ liệu cũ dạng string sẽ được tự động convert:
```
// Old format (still supported)
["Yêu cầu 1", "Yêu cầu 2"]

// Automatically converted to:
[{"title": "", "description": "Yêu cầu 1"}, {"title": "", "description": "Yêu cầu 2"}]
```

### application_process
```
meta_key: application_process
meta_value: [
  {"title": "Bước 1", "description": "Mô tả bước 1"},
  {"title": "Bước 2", "description": "Mô tả bước 2"}
]
```

## 7. Responsive Design

- **Mobile**: Layout stack, touch-friendly
- **Tablet**: Optimized spacing
- **Desktop**: Full layout với sidebar

## 8. Browser Support

- **Modern browsers**: Chrome, Firefox, Safari, Edge
- **IE**: Không hỗ trợ (do sử dụng CSS Grid và Flexbox)

## 9. Performance

- **CSS**: Chỉ load trên single scholarship pages
- **JavaScript**: Chỉ load trong admin khi edit scholarship
- **Database**: Efficient meta queries

## 10. Troubleshooting

### Không Hiển Thị Custom Fields
1. Kiểm tra user permissions
2. Đảm bảo đang edit scholarship post type
3. Clear cache nếu có

### JavaScript Không Hoạt Động
1. Kiểm tra console errors
2. Đảm bảo jQuery được load
3. Kiểm tra file paths

### Styling Bị Lỗi
1. Kiểm tra CSS conflicts
2. Clear browser cache
3. Kiểm tra responsive breakpoints

## 11. Future Enhancements

- [ ] Import/Export functionality
- [ ] Template system cho common requirements
- [ ] Rich text editor cho descriptions
- [ ] Image upload cho process steps
- [ ] Conditional logic
- [ ] Multi-language support

## 12. Support

Để được hỗ trợ, vui lòng:
1. Kiểm tra console errors
2. Cung cấp screenshots
3. Mô tả chi tiết vấn đề
4. Cung cấp browser và version
