# Hướng Dẫn Sử Dụng Mega Menu - IntHub Theme

## Tổng Quan

Mega Menu là một hệ thống navigation đa cấp với khả năng hiển thị hình ảnh, mô tả và layout dạng grid. Hệ thống này được thiết kế theo các tiêu chuẩn phát triển của dự án với việc tách biệt hoàn toàn CSS, JavaScript và HTML.

## Cấu Trúc Files

### 1. CSS Files
- **`assets/css/mega-menu.css`**: Chứa tất cả styling cho mega menu
  - Grid layouts (2, 3, 4 columns)
  - Animations và transitions
  - Responsive design
  - Dark mode support

### 2. JavaScript Files
- **`assets/js/mega-menu.js`**: Logic chính cho mega menu
  - Hover/click handlers
  - Animation controls
  - Keyboard navigation
  - Mobile responsive behavior

- **`assets/js/mega-menu-admin.js`**: Admin interface functionality
  - Image upload handling
  - Field toggles
  - Preview functionality

### 3. PHP Files
- **`inc/mega-menu-fields.php`**: Custom fields cho menu items
- **`functions.php`**: Walker class và enqueue scripts

## Cách Sử Dụng

### 1. Tạo Mega Menu

1. **Truy cập Admin Menu**:
   - Đi tới `Appearance > Menus`
   - Chọn menu muốn chỉnh sửa

2. **Cấu hình Menu Item**:
   - Mở rộng menu item muốn làm mega menu
   - Tìm section "Mega Menu Settings"
   - Check "Enable Mega Menu"

3. **Cấu hình Options**:
   - **Image URL**: Upload hoặc nhập URL hình ảnh đại diện
   - **Description**: Mô tả ngắn cho category
   - **Number of Columns**: Chọn 2, 3, 4 columns hoặc Auto
   - **Featured Item**: Check để làm item nổi bật (chiếm nhiều không gian hơn)

### 2. Cấu Trúc Menu Đa Cấp

```
Main Menu Item (Level 1) - Enable Mega Menu
├── Category 1 (Level 2) - Có thể có image + description
│   ├── Sub-item 1 (Level 3)
│   ├── Sub-item 2 (Level 3)
│   └── Sub-item 3 (Level 3)
├── Category 2 (Level 2) - Featured item
│   ├── Sub-item 1 (Level 3)
│   └── Sub-item 2 (Level 3)
└── Category 3 (Level 2)
    └── Sub-item 1 (Level 3)
```

### 3. Responsive Behavior

- **Desktop (≥768px)**: Hover để hiển thị mega menu
- **Mobile (<768px)**: Click để toggle, hiển thị dạng accordion

## Customization

### 1. Thay Đổi Styling

Chỉnh sửa file `assets/css/mega-menu.css`:

```css
/* Thay đổi màu chủ đạo */
.mega-menu {
    border-top-color: #your-color;
}

/* Thay đổi hover effect */
.mega-menu-item:hover {
    background: #your-background;
    border-color: #your-border;
}
```

### 2. Thay Đổi Animation

Trong `assets/js/mega-menu.js`:

```javascript
// Thay đổi thời gian delay
config: {
    hoverDelay: 200, // Tăng/giảm delay
    animationDuration: 400, // Thay đổi thời gian animation
}
```

### 3. Thêm Custom Fields

Trong `inc/mega-menu-fields.php`, thêm field mới:

```php
// Thêm field mới
<p class="field-mega-menu-custom description description-wide">
    <label for="edit-menu-item-mega-menu-custom-<?php echo $item_id; ?>">
        Custom Field<br />
        <input type="text" 
               id="edit-menu-item-mega-menu-custom-<?php echo $item_id; ?>" 
               name="menu-item-mega-menu-custom[<?php echo $item_id; ?>]" 
               value="<?php echo esc_attr($custom_value); ?>" />
    </label>
</p>
```

## Best Practices

### 1. Tổ Chức Content
- **Level 1**: Menu chính (Dịch vụ, Sản phẩm, etc.)
- **Level 2**: Categories với hình ảnh và mô tả
- **Level 3**: Specific links/pages

### 2. Hình Ảnh
- **Kích thước khuyến nghị**: 300x200px
- **Format**: JPG, PNG, WebP
- **Tối ưu**: Nén hình ảnh để tăng tốc độ load

### 3. Mô Tả
- **Độ dài**: 50-100 ký tự
- **Nội dung**: Mô tả ngắn gọn, hấp dẫn

### 4. Columns Layout
- **2 Columns**: Phù hợp cho 2-4 categories
- **3 Columns**: Phù hợp cho 3-6 categories  
- **4 Columns**: Phù hợp cho 4-8 categories
- **Auto**: Tự động điều chỉnh dựa trên số lượng items

## Troubleshooting

### 1. Mega Menu Không Hiển Thị
- Kiểm tra "Enable Mega Menu" đã được check
- Đảm bảo menu item có sub-items
- Kiểm tra CSS và JS đã được enqueue

### 2. Hình Ảnh Không Hiển Thị
- Kiểm tra URL hình ảnh có chính xác
- Đảm bảo hình ảnh accessible từ frontend
- Kiểm tra permissions của file

### 3. Animation Không Smooth
- Kiểm tra browser có hỗ trợ CSS transitions
- Đảm bảo không có CSS conflicts
- Kiểm tra performance của trang

### 4. Mobile Responsive Issues
- Kiểm tra viewport meta tag
- Đảm bảo Tailwind CSS được load
- Test trên nhiều devices khác nhau

## Advanced Features

### 1. Custom Walker Class
Walker class `IntHub_Walker_Nav_Menu` đã được customize để:
- Detect mega menu enabled items
- Generate proper HTML structure
- Handle multi-level navigation
- Support featured items

### 2. JavaScript API
Mega menu expose global object `window.IntHubMegaMenu`:

```javascript
// Programmatically show mega menu
IntHubMegaMenu.showMegaMenu($menuItem);

// Hide all mega menus
IntHubMegaMenu.hideAllMegaMenus();

// Check if mobile
IntHubMegaMenu.isMobile();
```

### 3. Hooks và Filters
Có thể extend functionality thông qua WordPress hooks:

```php
// Filter mega menu data
add_filter('inthub_mega_menu_data', function($data, $item_id) {
    // Modify mega menu data
    return $data;
}, 10, 2);
```

## Performance Optimization

### 1. CSS Optimization
- CSS được minify trong production
- Sử dụng CSS Grid thay vì Flexbox cho better performance
- Optimize animations với `transform` và `opacity`

### 2. JavaScript Optimization
- Event delegation để giảm memory usage
- Throttle resize events
- Lazy load mega menu content nếu cần

### 3. Image Optimization
- Sử dụng responsive images
- Implement lazy loading
- Optimize image formats (WebP)

## Browser Support

- **Modern Browsers**: Full support
- **IE11**: Basic support (no CSS Grid)
- **Mobile Browsers**: Full responsive support
- **Screen Readers**: Full accessibility support

## Changelog

### Version 1.0.0
- Initial release
- Basic mega menu functionality
- Responsive design
- Admin interface
- Accessibility support
