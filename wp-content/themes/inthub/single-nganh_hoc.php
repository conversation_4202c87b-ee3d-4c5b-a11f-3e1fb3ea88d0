<?php
/**
 * Single template for <PERSON><PERSON>h <PERSON>c (Major/Field of Study) post type
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

while (have_posts()) : the_post();
    // Get custom fields
    $ma_nganh_hoc = get_post_meta(get_the_ID(), 'ma_nganh_hoc', true);
    $thoi_gian_dao_tao = get_post_meta(get_the_ID(), 'thoi_gian_dao_tao', true);
    $bang_cap = get_post_meta(get_the_ID(), 'bang_cap', true);
    $hoc_phi_uoc_tinh = get_post_meta(get_the_ID(), 'hoc_phi_uoc_tinh', true);
    $mon_hoc_chinh = get_post_meta(get_the_ID(), 'mon_hoc_chinh', true);
    $mo_ta_chi_tiet = get_post_meta(get_the_ID(), 'mo_ta_chi_tiet', true);
    $dieu_kien_dau_vao = get_post_meta(get_the_ID(), 'dieu_kien_dau_vao', true);
    $co_hoi_nghe_nghiep = get_post_meta(get_the_ID(), 'co_hoi_nghe_nghiep', true);
    
    // Get taxonomies
    $linh_vuc_terms = get_the_terms(get_the_ID(), 'linh_vuc');
    $cap_do_terms = get_the_terms(get_the_ID(), 'cap_do_dao_tao');
    
    // Get degree display name
    $bang_cap_names = array(
        'cu-nhan' => 'Cử nhân',
        'ky-su' => 'Kỹ sư',
        'bac-si' => 'Bác sĩ',
        'duoc-si' => 'Dược sĩ',
        'thac-si' => 'Thạc sĩ',
        'tien-si' => 'Tiến sĩ',
        'cao-dang' => 'Cao đẳng',
        'chung-chi' => 'Chứng chỉ'
    );
    $bang_cap_display = isset($bang_cap_names[$bang_cap]) ? $bang_cap_names[$bang_cap] : $bang_cap;
    ?>

    <div class="container mx-auto px-4 single-nganh-hoc">
        
        <!-- Header Section -->
        <div class="nganh-hoc-header">
            <div class="text-center">
                <h1 class="title"><?php the_title(); ?></h1>
                
                <div class="meta-info">
                    <?php if ($ma_nganh_hoc): ?>
                        <span class="meta-item">
                            <i class="fas fa-code"></i>
                            Mã ngành: <?php echo esc_html($ma_nganh_hoc); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($thoi_gian_dao_tao): ?>
                        <span class="meta-item">
                            <i class="fas fa-clock"></i>
                            Thời gian: <?php echo esc_html($thoi_gian_dao_tao); ?>
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($bang_cap_display): ?>
                        <span class="meta-item">
                            <i class="fas fa-graduation-cap"></i>
                            Bằng cấp: <?php echo esc_html($bang_cap_display); ?>
                        </span>
                    <?php endif; ?>
                </div>
                
                <!-- Taxonomies -->
                <div class="taxonomy-tags mt-4">
                    <?php if ($linh_vuc_terms && !is_wp_error($linh_vuc_terms)): ?>
                        <?php foreach ($linh_vuc_terms as $term): ?>
                            <a href="<?php echo get_term_link($term); ?>" class="tag linh-vuc">
                                <?php echo esc_html($term->name); ?>
                            </a>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <?php if ($cap_do_terms && !is_wp_error($cap_do_terms)): ?>
                        <?php foreach ($cap_do_terms as $term): ?>
                            <a href="<?php echo get_term_link($term); ?>" class="tag cap-do">
                                <?php echo esc_html($term->name); ?>
                            </a>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Content Section -->
        <div class="nganh-hoc-content">
            
            <!-- Main Content -->
            <div class="main-content">
                
                <!-- Featured Image -->
                <?php if (has_post_thumbnail()): ?>
                    <div class="featured-image mb-6">
                        <?php the_post_thumbnail('large', array('class' => 'w-full h-64 object-cover rounded-lg')); ?>
                    </div>
                <?php endif; ?>
                
                <!-- Excerpt -->
                <?php if (has_excerpt()): ?>
                    <div class="content-section">
                        <div class="excerpt text-lg text-gray-600 italic border-l-4 border-blue-500 pl-4 mb-6">
                            <?php the_excerpt(); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Main Content -->
                <?php if (get_the_content()): ?>
                    <div class="content-section">
                        <h2>Giới thiệu chung</h2>
                        <div class="content">
                            <?php the_content(); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Detailed Description -->
                <?php if ($mo_ta_chi_tiet): ?>
                    <div class="content-section">
                        <h2>Mô tả chi tiết</h2>
                        <div class="content">
                            <?php echo wp_kses_post($mo_ta_chi_tiet); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Main Subjects -->
                <?php if ($mon_hoc_chinh): ?>
                    <div class="content-section">
                        <h2>Môn học chính</h2>
                        <div class="subjects-list">
                            <?php 
                            $subjects = explode("\n", $mon_hoc_chinh);
                            if (!empty($subjects)): ?>
                                <ul class="list-disc list-inside space-y-2">
                                    <?php foreach ($subjects as $subject): ?>
                                        <?php if (trim($subject)): ?>
                                            <li><?php echo esc_html(trim($subject)); ?></li>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Entry Requirements -->
                <?php if ($dieu_kien_dau_vao): ?>
                    <div class="content-section">
                        <h2>Điều kiện đầu vào</h2>
                        <div class="content">
                            <?php echo wp_kses_post($dieu_kien_dau_vao); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Career Opportunities -->
                <?php if ($co_hoi_nghe_nghiep): ?>
                    <div class="content-section">
                        <h2>Cơ hội nghề nghiệp</h2>
                        <div class="content">
                            <?php echo wp_kses_post($co_hoi_nghe_nghiep); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
            </div>
            
            <!-- Sidebar -->
            <div class="sidebar-content">
                
                <!-- Quick Info -->
                <div class="info-box">
                    <h3>Thông tin nhanh</h3>
                    <ul class="info-list">
                        <?php if ($ma_nganh_hoc): ?>
                            <li>
                                <span class="label">Mã ngành:</span>
                                <span class="value"><?php echo esc_html($ma_nganh_hoc); ?></span>
                            </li>
                        <?php endif; ?>
                        
                        <?php if ($thoi_gian_dao_tao): ?>
                            <li>
                                <span class="label">Thời gian đào tạo:</span>
                                <span class="value"><?php echo esc_html($thoi_gian_dao_tao); ?></span>
                            </li>
                        <?php endif; ?>
                        
                        <?php if ($bang_cap_display): ?>
                            <li>
                                <span class="label">Bằng cấp:</span>
                                <span class="value"><?php echo esc_html($bang_cap_display); ?></span>
                            </li>
                        <?php endif; ?>
                        
                        <?php if ($hoc_phi_uoc_tinh): ?>
                            <li>
                                <span class="label">Học phí ước tính:</span>
                                <span class="value"><?php echo esc_html($hoc_phi_uoc_tinh); ?></span>
                            </li>
                        <?php endif; ?>
                        
                        <li>
                            <span class="label">Ngày cập nhật:</span>
                            <span class="value"><?php echo get_the_modified_date('d/m/Y'); ?></span>
                        </li>
                    </ul>
                </div>
                
                <!-- Related Fields -->
                <?php if ($linh_vuc_terms && !is_wp_error($linh_vuc_terms)): ?>
                    <div class="info-box">
                        <h3>Lĩnh vực liên quan</h3>
                        <div class="related-fields">
                            <?php foreach ($linh_vuc_terms as $term): ?>
                                <a href="<?php echo get_term_link($term); ?>" class="tag linh-vuc block mb-2">
                                    <?php echo esc_html($term->name); ?>
                                    <span class="count">(<?php echo $term->count; ?> ngành)</span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Actions -->
                <div class="info-box">
                    <h3>Hành động</h3>
                    <div class="action-buttons space-y-2">
                        <button class="btn-share w-full bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600 transition">
                            <i class="fas fa-share-alt mr-2"></i>
                            Chia sẻ
                        </button>
                        <button class="btn-print w-full bg-gray-500 text-white py-2 px-4 rounded hover:bg-gray-600 transition">
                            <i class="fas fa-print mr-2"></i>
                            In trang
                        </button>
                        <a href="<?php echo get_post_type_archive_link('nganh_hoc'); ?>" class="btn-back w-full bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600 transition block text-center">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Về danh sách
                        </a>
                    </div>
                </div>
                
            </div>
            
        </div>
        
        <!-- Related Posts -->
        <?php
        $related_posts = get_posts(array(
            'post_type' => 'nganh_hoc',
            'posts_per_page' => 3,
            'post__not_in' => array(get_the_ID()),
            'tax_query' => array(
                'relation' => 'OR',
                array(
                    'taxonomy' => 'linh_vuc',
                    'field' => 'term_id',
                    'terms' => wp_list_pluck($linh_vuc_terms ?: array(), 'term_id'),
                ),
                array(
                    'taxonomy' => 'cap_do_dao_tao',
                    'field' => 'term_id',
                    'terms' => wp_list_pluck($cap_do_terms ?: array(), 'term_id'),
                ),
            ),
        ));
        
        if ($related_posts): ?>
            <div class="related-posts mt-12">
                <h2 class="text-2xl font-bold mb-6 text-center">Ngành học liên quan</h2>
                <div class="nganh-hoc-grid">
                    <?php foreach ($related_posts as $post): setup_postdata($post); ?>
                        <?php get_template_part('template-parts/content', 'nganh-hoc-card'); ?>
                    <?php endforeach; wp_reset_postdata(); ?>
                </div>
            </div>
        <?php endif; ?>
        
    </div>

    <!-- Back to Top Button -->
    <div class="back-to-top">
        <i class="fas fa-arrow-up"></i>
    </div>

<?php endwhile;

get_footer(); ?>
