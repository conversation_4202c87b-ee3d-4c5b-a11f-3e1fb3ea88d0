<?php
/**
 * Archive template for <PERSON><PERSON><PERSON> Hoc (Major/Field of Study) post type
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header(); ?>

<div class="container mx-auto px-4 nganh-hoc-archive">
    
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title"><PERSON>h sách ngành học</h1>
        <p class="page-description">
            Khám phá các ngành học đa dạng với thông tin chi tiết về chương trình đào tạo, 
            điều kiện đầu vào và cơ hội nghề nghiệp.
        </p>
    </div>

    <!-- Filters Section -->
    <div class="nganh-hoc-filters">
        <h3><i class="fas fa-filter mr-2"></i>Lọc ngành học</h3>
        <form class="filter-form">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="filter-linh-vuc"><PERSON><PERSON><PERSON> vực</label>
                    <select id="filter-linh-vuc" name="linh_vuc">
                        <option value="">Tất cả lĩnh vực</option>
                        <?php
                        $linh_vuc_terms = get_terms(array(
                            'taxonomy' => 'linh_vuc',
                            'hide_empty' => true,
                        ));
                        foreach ($linh_vuc_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?> (<?php echo $term->count; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-cap-do">Cấp độ đào tạo</label>
                    <select id="filter-cap-do" name="cap_do_dao_tao">
                        <option value="">Tất cả cấp độ</option>
                        <?php
                        $cap_do_terms = get_terms(array(
                            'taxonomy' => 'cap_do_dao_tao',
                            'hide_empty' => true,
                        ));
                        foreach ($cap_do_terms as $term): ?>
                            <option value="<?php echo esc_attr($term->slug); ?>">
                                <?php echo esc_html($term->name); ?> (<?php echo $term->count; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label for="filter-search">Tìm kiếm</label>
                    <input type="text" id="filter-search" name="search" placeholder="Nhập tên ngành học...">
                </div>
            </div>
            
            <div class="filter-actions">
                <button type="submit" class="btn-filter">
                    <i class="fas fa-search mr-2"></i>Lọc
                </button>
                <button type="button" class="btn-reset">
                    <i class="fas fa-undo mr-2"></i>Đặt lại
                </button>
            </div>
        </form>
    </div>

    <!-- Results Count -->
    <div class="results-info mb-4">
        <p class="results-count text-gray-600">
            <?php
            global $wp_query;
            $total = $wp_query->found_posts;
            echo "Hiển thị {$total} ngành học";
            ?>
        </p>
    </div>

    <!-- Posts Grid -->
    <div class="nganh-hoc-grid">
        <?php if (have_posts()) : ?>
            <?php while (have_posts()) : the_post(); ?>
                <?php
                // Get custom fields
                $ma_nganh_hoc = get_post_meta(get_the_ID(), 'ma_nganh_hoc', true);
                $thoi_gian_dao_tao = get_post_meta(get_the_ID(), 'thoi_gian_dao_tao', true);
                $bang_cap = get_post_meta(get_the_ID(), 'bang_cap', true);
                $hoc_phi_uoc_tinh = get_post_meta(get_the_ID(), 'hoc_phi_uoc_tinh', true);
                
                // Get taxonomies
                $linh_vuc_terms = get_the_terms(get_the_ID(), 'linh_vuc');
                $cap_do_terms = get_the_terms(get_the_ID(), 'cap_do_dao_tao');
                
                // Get degree display name
                $bang_cap_names = array(
                    'cu-nhan' => 'Cử nhân',
                    'ky-su' => 'Kỹ sư',
                    'bac-si' => 'Bác sĩ',
                    'duoc-si' => 'Dược sĩ',
                    'thac-si' => 'Thạc sĩ',
                    'tien-si' => 'Tiến sĩ',
                    'cao-dang' => 'Cao đẳng',
                    'chung-chi' => 'Chứng chỉ'
                );
                $bang_cap_display = isset($bang_cap_names[$bang_cap]) ? $bang_cap_names[$bang_cap] : $bang_cap;
                
                // Build CSS classes for filtering
                $card_classes = array('nganh-hoc-card');
                if ($linh_vuc_terms && !is_wp_error($linh_vuc_terms)) {
                    foreach ($linh_vuc_terms as $term) {
                        $card_classes[] = 'linh-vuc-' . $term->slug;
                    }
                }
                if ($cap_do_terms && !is_wp_error($cap_do_terms)) {
                    foreach ($cap_do_terms as $term) {
                        $card_classes[] = 'cap-do-' . $term->slug;
                    }
                }
                ?>
                
                <div class="<?php echo implode(' ', $card_classes); ?>">
                    <!-- Card Header -->
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php the_permalink(); ?>" class="text-white hover:text-gray-200">
                                <?php the_title(); ?>
                            </a>
                        </h3>
                        <div class="card-meta">
                            <?php if ($ma_nganh_hoc): ?>
                                <span><i class="fas fa-code mr-1"></i><?php echo esc_html($ma_nganh_hoc); ?></span>
                            <?php endif; ?>
                            <?php if ($thoi_gian_dao_tao): ?>
                                <span><i class="fas fa-clock mr-1"></i><?php echo esc_html($thoi_gian_dao_tao); ?></span>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Card Body -->
                    <div class="card-body">
                        <!-- Featured Image -->
                        <?php if (has_post_thumbnail()): ?>
                            <div class="card-image mb-3">
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail('medium', array('class' => 'w-full h-32 object-cover rounded')); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Excerpt -->
                        <?php if (has_excerpt()): ?>
                            <div class="card-excerpt">
                                <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Details Grid -->
                        <div class="card-details">
                            <?php if ($bang_cap_display): ?>
                                <div class="detail-item">
                                    <i class="fas fa-graduation-cap icon"></i>
                                    <span class="label">Bằng cấp:</span>
                                    <span class="value"><?php echo esc_html($bang_cap_display); ?></span>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($hoc_phi_uoc_tinh): ?>
                                <div class="detail-item">
                                    <i class="fas fa-dollar-sign icon"></i>
                                    <span class="label">Học phí:</span>
                                    <span class="value"><?php echo esc_html($hoc_phi_uoc_tinh); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <!-- Card Footer -->
                    <div class="card-footer">
                        <!-- Tags -->
                        <div class="card-tags">
                            <?php if ($linh_vuc_terms && !is_wp_error($linh_vuc_terms)): ?>
                                <?php foreach ($linh_vuc_terms as $term): ?>
                                    <a href="<?php echo get_term_link($term); ?>" class="tag linh-vuc">
                                        <?php echo esc_html($term->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            <?php endif; ?>
                            
                            <?php if ($cap_do_terms && !is_wp_error($cap_do_terms)): ?>
                                <?php foreach ($cap_do_terms as $term): ?>
                                    <a href="<?php echo get_term_link($term); ?>" class="tag cap-do">
                                        <?php echo esc_html($term->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Read More Button -->
                        <a href="<?php the_permalink(); ?>" class="btn-read-more">
                            Xem chi tiết <i class="fas fa-arrow-right ml-2"></i>
                        </a>
                    </div>
                </div>
                
            <?php endwhile; ?>
        <?php else : ?>
            <div class="no-posts col-span-full text-center py-12">
                <i class="fas fa-search text-6xl text-gray-300 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">Không tìm thấy ngành học nào</h3>
                <p class="text-gray-500">Hãy thử thay đổi bộ lọc hoặc từ khóa tìm kiếm.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if (have_posts()) : ?>
        <div class="pagination-wrapper mt-12">
            <?php
            $pagination = paginate_links(array(
                'total' => $wp_query->max_num_pages,
                'current' => max(1, get_query_var('paged')),
                'format' => '?paged=%#%',
                'show_all' => false,
                'end_size' => 1,
                'mid_size' => 2,
                'prev_next' => true,
                'prev_text' => '<i class="fas fa-chevron-left"></i> Trước',
                'next_text' => 'Sau <i class="fas fa-chevron-right"></i>',
                'type' => 'array'
            ));
            
            if ($pagination) : ?>
                <nav class="pagination" aria-label="Phân trang ngành học">
                    <ul class="pagination-list">
                        <?php foreach ($pagination as $page) : ?>
                            <li class="pagination-item"><?php echo $page; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <!-- Load More Button (Alternative to pagination) -->
    <?php if ($wp_query->max_num_pages > 1 && get_query_var('paged') < $wp_query->max_num_pages) : ?>
        <div class="load-more-wrapper text-center mt-8" style="display: none;">
            <button class="btn-load-more" 
                    data-page="<?php echo get_query_var('paged') ?: 1; ?>" 
                    data-max-pages="<?php echo $wp_query->max_num_pages; ?>">
                <i class="fas fa-plus mr-2"></i>Tải thêm ngành học
            </button>
        </div>
    <?php endif; ?>

</div>

<!-- Add some inline styles for pagination -->
<style>
.pagination-list {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.pagination-item a,
.pagination-item span {
    display: block;
    padding: 0.75rem 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    text-decoration: none;
    color: #374151;
    transition: all 0.2s;
}

.pagination-item a:hover {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-item .current {
    background-color: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination-item .dots {
    border: none;
    background: none;
    color: #9ca3af;
}
</style>

<?php get_footer(); ?>
