<?php
/**
 * General Helper Functions
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get Theme Option from Customizer
 */
function inthub_get_theme_option($option_name, $default = '') {
    return get_theme_mod($option_name, $default);
}

/**
 * Display Contact Information
 */
function inthub_display_contact_info($type = 'all') {
    $phone = inthub_get_theme_option('inthub_phone', '+84 28 1234 5678');
    $email = inthub_get_theme_option('inthub_email', '<EMAIL>');
    $address = inthub_get_theme_option('inthub_address', '123 <PERSON><PERSON><PERSON><PERSON>, Quận 1, TP<PERSON> <PERSON><PERSON>');
    
    $output = '';
    
    switch ($type) {
        case 'phone':
            if ($phone) {
                $output = '<a href="tel:' . esc_attr($phone) . '" class="contact-phone">' . esc_html($phone) . '</a>';
            }
            break;
            
        case 'email':
            if ($email) {
                $output = '<a href="mailto:' . esc_attr($email) . '" class="contact-email">' . esc_html($email) . '</a>';
            }
            break;
            
        case 'address':
            if ($address) {
                $output = '<span class="contact-address">' . esc_html($address) . '</span>';
            }
            break;
            
        default:
            $output = '<div class="contact-info">';
            if ($phone) {
                $output .= '<div class="contact-item"><i class="fas fa-phone"></i> <a href="tel:' . esc_attr($phone) . '">' . esc_html($phone) . '</a></div>';
            }
            if ($email) {
                $output .= '<div class="contact-item"><i class="fas fa-envelope"></i> <a href="mailto:' . esc_attr($email) . '">' . esc_html($email) . '</a></div>';
            }
            if ($address) {
                $output .= '<div class="contact-item"><i class="fas fa-map-marker-alt"></i> ' . esc_html($address) . '</div>';
            }
            $output .= '</div>';
            break;
    }
    
    return $output;
}

/**
 * Display Social Media Links
 */
function inthub_display_social_links($class = 'social-links') {
    $facebook = inthub_get_theme_option('inthub_facebook');
    $twitter = inthub_get_theme_option('inthub_twitter');
    $instagram = inthub_get_theme_option('inthub_instagram');
    $linkedin = inthub_get_theme_option('inthub_linkedin');
    
    $output = '<div class="' . esc_attr($class) . '">';
    
    if ($facebook) {
        $output .= '<a href="' . esc_url($facebook) . '" target="_blank" rel="noopener" class="social-link facebook"><i class="fab fa-facebook-f"></i></a>';
    }
    
    if ($twitter) {
        $output .= '<a href="' . esc_url($twitter) . '" target="_blank" rel="noopener" class="social-link twitter"><i class="fab fa-twitter"></i></a>';
    }
    
    if ($instagram) {
        $output .= '<a href="' . esc_url($instagram) . '" target="_blank" rel="noopener" class="social-link instagram"><i class="fab fa-instagram"></i></a>';
    }
    
    if ($linkedin) {
        $output .= '<a href="' . esc_url($linkedin) . '" target="_blank" rel="noopener" class="social-link linkedin"><i class="fab fa-linkedin-in"></i></a>';
    }
    
    $output .= '</div>';
    
    return $output;
}

/**
 * Get Post Reading Time
 */
function inthub_get_reading_time($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $content = get_post_field('post_content', $post_id);
    $word_count = str_word_count(strip_tags($content));
    $reading_time = ceil($word_count / 200); // Average reading speed: 200 words per minute
    
    return $reading_time;
}

/**
 * Display Reading Time
 */
function inthub_display_reading_time($post_id = null) {
    $reading_time = inthub_get_reading_time($post_id);
    
    if ($reading_time <= 1) {
        return '<span class="reading-time">' . __('1 phút đọc', 'inthub') . '</span>';
    } else {
        return '<span class="reading-time">' . sprintf(__('%d phút đọc', 'inthub'), $reading_time) . '</span>';
    }
}

/**
 * Get Post Views Count
 */
function inthub_get_post_views($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $views = get_post_meta($post_id, 'post_views', true);
    return $views ? intval($views) : 0;
}

/**
 * Update Post Views Count
 */
function inthub_update_post_views($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $views = inthub_get_post_views($post_id);
    $views++;
    
    update_post_meta($post_id, 'post_views', $views);
}

/**
 * Display Post Views
 */
function inthub_display_post_views($post_id = null) {
    $views = inthub_get_post_views($post_id);
    
    if ($views == 0) {
        return '<span class="post-views">' . __('Chưa có lượt xem', 'inthub') . '</span>';
    } elseif ($views == 1) {
        return '<span class="post-views">' . __('1 lượt xem', 'inthub') . '</span>';
    } else {
        return '<span class="post-views">' . sprintf(__('%s lượt xem', 'inthub'), number_format($views)) . '</span>';
    }
}

/**
 * Format Date in Vietnamese
 */
function inthub_format_date($date, $format = 'd/m/Y') {
    if (empty($date)) {
        return '';
    }
    
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    return date($format, $timestamp);
}

/**
 * Get Time Ago in Vietnamese
 */
function inthub_time_ago($date) {
    $timestamp = is_numeric($date) ? $date : strtotime($date);
    $diff = current_time('timestamp') - $timestamp;
    
    if ($diff < 60) {
        return __('Vừa xong', 'inthub');
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return sprintf(__('%d phút trước', 'inthub'), $minutes);
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return sprintf(__('%d giờ trước', 'inthub'), $hours);
    } elseif ($diff < 2592000) {
        $days = floor($diff / 86400);
        return sprintf(__('%d ngày trước', 'inthub'), $days);
    } else {
        return inthub_format_date($timestamp);
    }
}

/**
 * Truncate Text
 */
function inthub_truncate_text($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Get Featured Posts
 */
function inthub_get_featured_posts($post_type = 'post', $limit = 5) {
    $args = array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => 'featured_post',
                'value' => '1',
                'compare' => '='
            )
        ),
        'orderby' => 'date',
        'order' => 'DESC'
    );
    
    return get_posts($args);
}

/**
 * Get Popular Posts (by views)
 */
function inthub_get_popular_posts($post_type = 'post', $limit = 5, $days = 30) {
    $args = array(
        'post_type' => $post_type,
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_key' => 'post_views',
        'orderby' => 'meta_value_num',
        'order' => 'DESC',
        'date_query' => array(
            array(
                'after' => $days . ' days ago'
            )
        )
    );
    
    return get_posts($args);
}

/**
 * Generate Breadcrumbs
 */
function inthub_generate_breadcrumbs() {
    $breadcrumbs = array();
    
    // Home
    $breadcrumbs[] = array(
        'title' => __('Trang chủ', 'inthub'),
        'url' => home_url('/'),
        'current' => is_front_page()
    );
    
    if (is_single()) {
        $post_type = get_post_type();
        
        // Add post type archive
        if ($post_type !== 'post') {
            $post_type_object = get_post_type_object($post_type);
            if ($post_type_object && $post_type_object->has_archive) {
                $breadcrumbs[] = array(
                    'title' => $post_type_object->labels->name,
                    'url' => get_post_type_archive_link($post_type)
                );
            }
        }
        
        // Add categories/taxonomies
        $taxonomies = get_object_taxonomies($post_type, 'objects');
        foreach ($taxonomies as $taxonomy) {
            if ($taxonomy->hierarchical) {
                $terms = get_the_terms(get_the_ID(), $taxonomy->name);
                if ($terms && !is_wp_error($terms)) {
                    foreach ($terms as $term) {
                        $breadcrumbs[] = array(
                            'title' => $term->name,
                            'url' => get_term_link($term)
                        );
                    }
                }
                break; // Only show first hierarchical taxonomy
            }
        }
        
        // Current post
        $breadcrumbs[] = array(
            'title' => get_the_title(),
            'url' => get_permalink(),
            'current' => true
        );
        
    } elseif (is_page()) {
        // Page hierarchy
        $ancestors = get_post_ancestors(get_the_ID());
        $ancestors = array_reverse($ancestors);
        
        foreach ($ancestors as $ancestor) {
            $breadcrumbs[] = array(
                'title' => get_the_title($ancestor),
                'url' => get_permalink($ancestor)
            );
        }
        
        // Current page
        $breadcrumbs[] = array(
            'title' => get_the_title(),
            'url' => get_permalink(),
            'current' => true
        );
        
    } elseif (is_post_type_archive()) {
        $post_type_object = get_queried_object();
        $breadcrumbs[] = array(
            'title' => $post_type_object->labels->name,
            'url' => get_post_type_archive_link($post_type_object->name),
            'current' => true
        );
        
    } elseif (is_tax() || is_category() || is_tag()) {
        $term = get_queried_object();
        
        // Add post type archive if applicable
        if (is_tax()) {
            $post_types = get_taxonomy($term->taxonomy)->object_type;
            if (!empty($post_types)) {
                $post_type_object = get_post_type_object($post_types[0]);
                if ($post_type_object && $post_type_object->has_archive) {
                    $breadcrumbs[] = array(
                        'title' => $post_type_object->labels->name,
                        'url' => get_post_type_archive_link($post_types[0])
                    );
                }
            }
        }
        
        // Current term
        $breadcrumbs[] = array(
            'title' => $term->name,
            'url' => get_term_link($term),
            'current' => true
        );
    }
    
    return $breadcrumbs;
}

/**
 * Display Breadcrumbs
 */
function inthub_display_breadcrumbs($separator = ' / ') {
    $breadcrumbs = inthub_generate_breadcrumbs();
    
    if (empty($breadcrumbs) || count($breadcrumbs) <= 1) {
        return '';
    }
    
    $output = '<nav class="breadcrumbs" aria-label="' . __('Breadcrumb', 'inthub') . '">';
    $output .= '<ol class="breadcrumb-list">';
    
    foreach ($breadcrumbs as $index => $breadcrumb) {
        $is_current = isset($breadcrumb['current']) && $breadcrumb['current'];
        $is_last = $index === count($breadcrumbs) - 1;
        
        $output .= '<li class="breadcrumb-item' . ($is_current ? ' current' : '') . '">';
        
        if (!$is_current && !empty($breadcrumb['url'])) {
            $output .= '<a href="' . esc_url($breadcrumb['url']) . '">' . esc_html($breadcrumb['title']) . '</a>';
        } else {
            $output .= '<span>' . esc_html($breadcrumb['title']) . '</span>';
        }
        
        if (!$is_last) {
            $output .= '<span class="separator">' . esc_html($separator) . '</span>';
        }
        
        $output .= '</li>';
    }
    
    $output .= '</ol>';
    $output .= '</nav>';
    
    return $output;
}
