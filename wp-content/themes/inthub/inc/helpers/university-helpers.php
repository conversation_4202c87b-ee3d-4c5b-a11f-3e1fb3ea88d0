<?php
/**
 * University Helper Functions
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get University Meta Data
 */
function inthub_get_university_meta($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    return array(
        'university_name' => get_post_meta($post_id, 'university_name', true),
        'establishment_year' => get_post_meta($post_id, 'establishment_year', true),
        'location' => get_post_meta($post_id, 'location', true),
        'overview' => get_post_meta($post_id, 'overview', true),
        'website_url' => get_post_meta($post_id, 'website_url', true),
        'courses' => get_post_meta($post_id, 'courses', true),
        'country' => get_post_meta($post_id, 'country', true),
        'tuition_fee' => get_post_meta($post_id, 'tuition_fee', true),
        'admission_info' => get_post_meta($post_id, 'admission_info', true),
        'map' => get_post_meta($post_id, 'map', true),
        'video_url' => get_post_meta($post_id, 'video_url', true)
    );
}

/**
 * Get University Country Label
 */
function inthub_get_university_country_label($country_code) {
    $countries = array(
        'usa' => __('Hoa Kỳ', 'inthub'),
        'uk' => __('Anh', 'inthub'),
        'canada' => __('Canada', 'inthub'),
        'australia' => __('Úc', 'inthub'),
        'germany' => __('Đức', 'inthub'),
        'france' => __('Pháp', 'inthub'),
        'japan' => __('Nhật Bản', 'inthub'),
        'south_korea' => __('Hàn Quốc', 'inthub'),
        'singapore' => __('Singapore', 'inthub'),
        'netherlands' => __('Hà Lan', 'inthub'),
        'other' => __('Khác', 'inthub')
    );
    
    return isset($countries[$country_code]) ? $countries[$country_code] : $country_code;
}

/**
 * Get University Categories
 */
function inthub_get_university_categories($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    return get_the_terms($post_id, 'university_category');
}

/**
 * Display University Categories
 */
function inthub_display_university_categories($post_id = null, $separator = ', ') {
    $categories = inthub_get_university_categories($post_id);
    
    if ($categories && !is_wp_error($categories)) {
        $category_names = array();
        foreach ($categories as $category) {
            $category_names[] = '<a href="' . get_term_link($category) . '">' . esc_html($category->name) . '</a>';
        }
        return implode($separator, $category_names);
    }
    
    return '';
}

/**
 * Get Related Universities
 */
function inthub_get_related_universities($post_id = null, $limit = 4) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    // Get current university categories
    $categories = inthub_get_university_categories($post_id);
    
    if (!$categories || is_wp_error($categories)) {
        return array();
    }
    
    $category_ids = array();
    foreach ($categories as $category) {
        $category_ids[] = $category->term_id;
    }
    
    // Query for related universities
    $args = array(
        'post_type' => 'university',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'post__not_in' => array($post_id),
        'tax_query' => array(
            array(
                'taxonomy' => 'university_category',
                'field' => 'term_id',
                'terms' => $category_ids,
                'operator' => 'IN'
            )
        ),
        'orderby' => 'rand'
    );
    
    return get_posts($args);
}

/**
 * Get Universities by Country
 */
function inthub_get_universities_by_country($country, $limit = -1) {
    $args = array(
        'post_type' => 'university',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => 'country',
                'value' => $country,
                'compare' => '='
            )
        ),
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Get Universities by Category
 */
function inthub_get_universities_by_category($category_slug, $limit = -1) {
    $args = array(
        'post_type' => 'university',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'tax_query' => array(
            array(
                'taxonomy' => 'university_category',
                'field' => 'slug',
                'terms' => $category_slug
            )
        ),
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Get University Statistics
 */
function inthub_get_university_statistics() {
    $stats = array();
    
    // Total universities
    $stats['total'] = wp_count_posts('university')->publish;
    
    // Universities by country
    $countries = array('usa', 'uk', 'canada', 'australia', 'germany', 'france', 'japan', 'south_korea', 'singapore', 'netherlands');
    $stats['by_country'] = array();
    
    foreach ($countries as $country) {
        $count = get_posts(array(
            'post_type' => 'university',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => 'country',
                    'value' => $country,
                    'compare' => '='
                )
            ),
            'fields' => 'ids'
        ));
        
        $stats['by_country'][$country] = count($count);
    }
    
    // Universities by category
    $categories = get_terms(array(
        'taxonomy' => 'university_category',
        'hide_empty' => true
    ));
    
    $stats['by_category'] = array();
    if ($categories && !is_wp_error($categories)) {
        foreach ($categories as $category) {
            $stats['by_category'][$category->slug] = $category->count;
        }
    }
    
    return $stats;
}

/**
 * Format University Courses List
 */
function inthub_format_university_courses($courses_text) {
    if (empty($courses_text)) {
        return '';
    }
    
    $courses = explode("\n", $courses_text);
    $courses = array_filter(array_map('trim', $courses));
    
    if (empty($courses)) {
        return '';
    }
    
    $output = '<ul class="university-courses-list">';
    foreach ($courses as $course) {
        $output .= '<li>' . esc_html($course) . '</li>';
    }
    $output .= '</ul>';
    
    return $output;
}

/**
 * Get University Search Filters
 */
function inthub_get_university_search_filters() {
    return array(
        'countries' => array(
            'usa' => __('Hoa Kỳ', 'inthub'),
            'uk' => __('Anh', 'inthub'),
            'canada' => __('Canada', 'inthub'),
            'australia' => __('Úc', 'inthub'),
            'germany' => __('Đức', 'inthub'),
            'france' => __('Pháp', 'inthub'),
            'japan' => __('Nhật Bản', 'inthub'),
            'south_korea' => __('Hàn Quốc', 'inthub'),
            'singapore' => __('Singapore', 'inthub'),
            'netherlands' => __('Hà Lan', 'inthub'),
            'other' => __('Khác', 'inthub')
        ),
        'categories' => get_terms(array(
            'taxonomy' => 'university_category',
            'hide_empty' => true,
            'orderby' => 'name',
            'order' => 'ASC'
        ))
    );
}

/**
 * Check if University has Video
 */
function inthub_university_has_video($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $video_url = get_post_meta($post_id, 'video_url', true);
    return !empty($video_url);
}

/**
 * Get University Video Embed Code
 */
function inthub_get_university_video_embed($post_id = null, $width = 560, $height = 315) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $video_url = get_post_meta($post_id, 'video_url', true);
    
    if (empty($video_url)) {
        return '';
    }
    
    // Use WordPress oEmbed to get embed code
    return wp_oembed_get($video_url, array('width' => $width, 'height' => $height));
}

/**
 * Get University Breadcrumbs
 */
function inthub_get_university_breadcrumbs($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $breadcrumbs = array();
    
    // Home
    $breadcrumbs[] = array(
        'title' => __('Trang chủ', 'inthub'),
        'url' => home_url('/')
    );
    
    // Universities archive
    $breadcrumbs[] = array(
        'title' => __('Trường đại học', 'inthub'),
        'url' => get_post_type_archive_link('university')
    );
    
    // University categories
    $categories = inthub_get_university_categories($post_id);
    if ($categories && !is_wp_error($categories)) {
        foreach ($categories as $category) {
            $breadcrumbs[] = array(
                'title' => $category->name,
                'url' => get_term_link($category)
            );
        }
    }
    
    // Current university
    $breadcrumbs[] = array(
        'title' => get_the_title($post_id),
        'url' => get_permalink($post_id),
        'current' => true
    );
    
    return $breadcrumbs;
}
