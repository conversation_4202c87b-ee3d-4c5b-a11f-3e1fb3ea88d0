<?php
/**
 * Scholarship Helper Functions
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get Scholarship Meta Data
 */
function inthub_get_scholarship_meta($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    return array(
        'scholarship_name' => get_post_meta($post_id, 'scholarship_name', true),
        'scholarship_value' => get_post_meta($post_id, 'scholarship_value', true),
        'deadline' => get_post_meta($post_id, 'deadline', true),
        'eligibility' => get_post_meta($post_id, 'eligibility', true),
        'application_process' => get_post_meta($post_id, 'application_process', true),
        'provider' => get_post_meta($post_id, 'provider', true),
        'country' => get_post_meta($post_id, 'country', true),
        'level' => get_post_meta($post_id, 'level', true),
        'field_of_study' => get_post_meta($post_id, 'field_of_study', true),
        'website_url' => get_post_meta($post_id, 'website_url', true),
        'contact_info' => get_post_meta($post_id, 'contact_info', true)
    );
}

/**
 * Get Scholarship Country Label
 */
function inthub_get_scholarship_country_label($country_code) {
    $countries = array(
        'usa' => __('Hoa Kỳ', 'inthub'),
        'uk' => __('Anh', 'inthub'),
        'canada' => __('Canada', 'inthub'),
        'australia' => __('Úc', 'inthub'),
        'germany' => __('Đức', 'inthub'),
        'france' => __('Pháp', 'inthub'),
        'japan' => __('Nhật Bản', 'inthub'),
        'south_korea' => __('Hàn Quốc', 'inthub'),
        'singapore' => __('Singapore', 'inthub'),
        'netherlands' => __('Hà Lan', 'inthub'),
        'other' => __('Khác', 'inthub')
    );
    
    return isset($countries[$country_code]) ? $countries[$country_code] : $country_code;
}

/**
 * Get Scholarship Level Label
 */
function inthub_get_scholarship_level_label($level_code) {
    $levels = array(
        'undergraduate' => __('Đại học', 'inthub'),
        'master' => __('Thạc sĩ', 'inthub'),
        'phd' => __('Tiến sĩ', 'inthub'),
        'postdoc' => __('Sau tiến sĩ', 'inthub'),
        'all' => __('Tất cả bậc học', 'inthub')
    );
    
    return isset($levels[$level_code]) ? $levels[$level_code] : $level_code;
}

/**
 * Get Scholarship Categories
 */
function inthub_get_scholarship_categories($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    return get_the_terms($post_id, 'scholarship_category');
}

/**
 * Display Scholarship Categories
 */
function inthub_display_scholarship_categories($post_id = null, $separator = ', ') {
    $categories = inthub_get_scholarship_categories($post_id);
    
    if ($categories && !is_wp_error($categories)) {
        $category_names = array();
        foreach ($categories as $category) {
            $category_names[] = '<a href="' . get_term_link($category) . '">' . esc_html($category->name) . '</a>';
        }
        return implode($separator, $category_names);
    }
    
    return '';
}

/**
 * Get Scholarship Deadline Status
 */
function inthub_get_scholarship_deadline_status($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $deadline = get_post_meta($post_id, 'deadline', true);
    
    if (empty($deadline)) {
        return array(
            'status' => 'unknown',
            'message' => __('Chưa có thông tin', 'inthub'),
            'class' => 'text-gray-500',
            'days_left' => null
        );
    }
    
    $now = current_time('timestamp');
    $deadline_timestamp = strtotime($deadline);
    $days_left = floor(($deadline_timestamp - $now) / (60 * 60 * 24));
    
    if ($days_left < 0) {
        return array(
            'status' => 'expired',
            'message' => __('Đã hết hạn', 'inthub'),
            'class' => 'text-red-600',
            'days_left' => $days_left
        );
    } elseif ($days_left <= 7) {
        return array(
            'status' => 'urgent',
            'message' => sprintf(__('Còn %d ngày', 'inthub'), $days_left),
            'class' => 'text-red-600 font-bold',
            'days_left' => $days_left
        );
    } elseif ($days_left <= 30) {
        return array(
            'status' => 'soon',
            'message' => sprintf(__('Còn %d ngày', 'inthub'), $days_left),
            'class' => 'text-yellow-600 font-bold',
            'days_left' => $days_left
        );
    } else {
        return array(
            'status' => 'active',
            'message' => date('d/m/Y', $deadline_timestamp),
            'class' => 'text-green-600',
            'days_left' => $days_left
        );
    }
}

/**
 * Get Related Scholarships
 */
function inthub_get_related_scholarships($post_id = null, $limit = 4) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    // Get current scholarship data
    $meta = inthub_get_scholarship_meta($post_id);
    $categories = inthub_get_scholarship_categories($post_id);
    
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'post__not_in' => array($post_id),
        'orderby' => 'rand'
    );
    
    $meta_query = array('relation' => 'OR');
    $tax_query = array();
    
    // Match by country
    if (!empty($meta['country'])) {
        $meta_query[] = array(
            'key' => 'country',
            'value' => $meta['country'],
            'compare' => '='
        );
    }
    
    // Match by level
    if (!empty($meta['level'])) {
        $meta_query[] = array(
            'key' => 'level',
            'value' => $meta['level'],
            'compare' => '='
        );
    }
    
    // Match by categories
    if ($categories && !is_wp_error($categories)) {
        $category_ids = array();
        foreach ($categories as $category) {
            $category_ids[] = $category->term_id;
        }
        
        $tax_query[] = array(
            'taxonomy' => 'scholarship_category',
            'field' => 'term_id',
            'terms' => $category_ids,
            'operator' => 'IN'
        );
    }
    
    if (!empty($meta_query) && count($meta_query) > 1) {
        $args['meta_query'] = $meta_query;
    }
    
    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }
    
    return get_posts($args);
}

/**
 * Get Scholarships by Country
 */
function inthub_get_scholarships_by_country($country, $limit = -1) {
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => 'country',
                'value' => $country,
                'compare' => '='
            )
        ),
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Get Scholarships by Level
 */
function inthub_get_scholarships_by_level($level, $limit = -1) {
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => 'level',
                'value' => $level,
                'compare' => '='
            )
        ),
        'orderby' => 'title',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Get Active Scholarships (not expired)
 */
function inthub_get_active_scholarships($limit = -1) {
    $now = current_time('Y-m-d');
    
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            array(
                'key' => 'deadline',
                'value' => $now,
                'compare' => '>=',
                'type' => 'DATE'
            )
        ),
        'meta_key' => 'deadline',
        'orderby' => 'meta_value',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Get Expiring Scholarships (deadline within specified days)
 */
function inthub_get_expiring_scholarships($days = 30, $limit = -1) {
    $now = current_time('Y-m-d');
    $future = date('Y-m-d', strtotime("+{$days} days"));
    
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $limit,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => 'deadline',
                'value' => $now,
                'compare' => '>=',
                'type' => 'DATE'
            ),
            array(
                'key' => 'deadline',
                'value' => $future,
                'compare' => '<=',
                'type' => 'DATE'
            )
        ),
        'meta_key' => 'deadline',
        'orderby' => 'meta_value',
        'order' => 'ASC'
    );
    
    return get_posts($args);
}

/**
 * Get Scholarship Statistics
 */
function inthub_get_scholarship_statistics() {
    $stats = array();
    
    // Total scholarships
    $stats['total'] = wp_count_posts('scholarship')->publish;
    
    // Active scholarships
    $stats['active'] = count(inthub_get_active_scholarships());
    
    // Expiring soon (30 days)
    $stats['expiring_soon'] = count(inthub_get_expiring_scholarships(30));
    
    // By country
    $countries = array('usa', 'uk', 'canada', 'australia', 'germany', 'france', 'japan', 'south_korea', 'singapore', 'netherlands');
    $stats['by_country'] = array();
    
    foreach ($countries as $country) {
        $stats['by_country'][$country] = count(inthub_get_scholarships_by_country($country));
    }
    
    // By level
    $levels = array('undergraduate', 'master', 'phd', 'postdoc', 'all');
    $stats['by_level'] = array();
    
    foreach ($levels as $level) {
        $stats['by_level'][$level] = count(inthub_get_scholarships_by_level($level));
    }
    
    return $stats;
}

/**
 * Get Scholarship Search Filters
 */
function inthub_get_scholarship_search_filters() {
    return array(
        'countries' => array(
            'usa' => __('Hoa Kỳ', 'inthub'),
            'uk' => __('Anh', 'inthub'),
            'canada' => __('Canada', 'inthub'),
            'australia' => __('Úc', 'inthub'),
            'germany' => __('Đức', 'inthub'),
            'france' => __('Pháp', 'inthub'),
            'japan' => __('Nhật Bản', 'inthub'),
            'south_korea' => __('Hàn Quốc', 'inthub'),
            'singapore' => __('Singapore', 'inthub'),
            'netherlands' => __('Hà Lan', 'inthub'),
            'other' => __('Khác', 'inthub')
        ),
        'levels' => array(
            'undergraduate' => __('Đại học', 'inthub'),
            'master' => __('Thạc sĩ', 'inthub'),
            'phd' => __('Tiến sĩ', 'inthub'),
            'postdoc' => __('Sau tiến sĩ', 'inthub'),
            'all' => __('Tất cả bậc học', 'inthub')
        ),
        'categories' => get_terms(array(
            'taxonomy' => 'scholarship_category',
            'hide_empty' => true,
            'orderby' => 'name',
            'order' => 'ASC'
        ))
    );
}

/**
 * Get Scholarship Breadcrumbs
 */
function inthub_get_scholarship_breadcrumbs($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }
    
    $breadcrumbs = array();
    
    // Home
    $breadcrumbs[] = array(
        'title' => __('Trang chủ', 'inthub'),
        'url' => home_url('/')
    );
    
    // Scholarships archive
    $breadcrumbs[] = array(
        'title' => __('Học bổng', 'inthub'),
        'url' => get_post_type_archive_link('scholarship')
    );
    
    // Scholarship categories
    $categories = inthub_get_scholarship_categories($post_id);
    if ($categories && !is_wp_error($categories)) {
        foreach ($categories as $category) {
            $breadcrumbs[] = array(
                'title' => $category->name,
                'url' => get_term_link($category)
            );
        }
    }
    
    // Current scholarship
    $breadcrumbs[] = array(
        'title' => get_the_title($post_id),
        'url' => get_permalink($post_id),
        'current' => true
    );
    
    return $breadcrumbs;
}
