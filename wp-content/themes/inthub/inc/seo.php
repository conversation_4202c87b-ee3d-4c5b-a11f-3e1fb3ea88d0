<?php
/**
 * SEO and Meta Functions
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * SEO and Meta for University Taxonomy
 */
function inthub_taxonomy_meta() {
    if (is_tax('university_category')) {
        $term = get_queried_object();

        // Meta description
        $meta_description = $term->description;
        if (empty($meta_description)) {
            $meta_description = sprintf(
                __('Khám phá %d trường đại học trong danh mục %s. Tìm hiểu thông tin chi tiết về học phí, học bổng và yêu cầu tuyển sinh.', 'inthub'),
                $term->count,
                $term->name
            );
        }

        // Limit meta description length
        if (strlen($meta_description) > 160) {
            $meta_description = substr($meta_description, 0, 157) . '...';
        }

        echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";

        // Open Graph tags
        echo '<meta property="og:title" content="' . esc_attr($term->name . ' - ' . get_bloginfo('name')) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($meta_description) . '">' . "\n";
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(get_term_link($term)) . '">' . "\n";

        // Twitter Card tags
        echo '<meta name="twitter:card" content="summary">' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($term->name . ' - ' . get_bloginfo('name')) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr($meta_description) . '">' . "\n";

        // Canonical URL
        echo '<link rel="canonical" href="' . esc_url(get_term_link($term)) . '">' . "\n";
    }

    // Handle scholarship category taxonomy
    if (is_tax('scholarship_category')) {
        $term = get_queried_object();

        // Meta description
        $meta_description = $term->description;
        if (empty($meta_description)) {
            $meta_description = sprintf(
                __('Khám phá %d học bổng trong danh mục %s. Tìm hiểu thông tin chi tiết về giá trị học bổng, yêu cầu ứng tuyển và quy trình đăng ký.', 'inthub'),
                $term->count,
                $term->name
            );
        }

        // Limit meta description length
        if (strlen($meta_description) > 160) {
            $meta_description = substr($meta_description, 0, 157) . '...';
        }

        echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";

        // Open Graph tags
        echo '<meta property="og:title" content="' . esc_attr($term->name . ' - ' . get_bloginfo('name')) . '">' . "\n";
        echo '<meta property="og:description" content="' . esc_attr($meta_description) . '">' . "\n";
        echo '<meta property="og:type" content="website">' . "\n";
        echo '<meta property="og:url" content="' . esc_url(get_term_link($term)) . '">' . "\n";

        // Twitter Card tags
        echo '<meta name="twitter:card" content="summary">' . "\n";
        echo '<meta name="twitter:title" content="' . esc_attr($term->name . ' - ' . get_bloginfo('name')) . '">' . "\n";
        echo '<meta name="twitter:description" content="' . esc_attr($meta_description) . '">' . "\n";

        // Canonical URL
        echo '<link rel="canonical" href="' . esc_url(get_term_link($term)) . '">' . "\n";
    }
}
add_action('wp_head', 'inthub_taxonomy_meta');

/**
 * Add university taxonomy to sitemap (if using Yoast SEO)
 */
function inthub_add_university_taxonomy_to_sitemap($provider, $name) {
    if ($name === 'taxonomy' && class_exists('WPSEO_Taxonomy_Sitemap_Provider')) {
        $provider->taxonomies[] = 'university_category';
    }
    return $provider;
}
add_filter('wpseo_sitemap_taxonomy_content', 'inthub_add_university_taxonomy_to_sitemap', 10, 2);
