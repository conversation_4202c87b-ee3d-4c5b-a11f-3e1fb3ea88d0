<?php
/**
 * Nganh Hoc Meta Boxes
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Nganh Hoc Meta Boxes
 */
function inthub_add_nganh_hoc_meta_boxes() {
    add_meta_box(
        'nganh_hoc_details',
        __('Chi tiết ngành học', 'inthub'),
        'inthub_nganh_hoc_details_callback',
        'nganh_hoc',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'inthub_add_nganh_hoc_meta_boxes');

/**
 * Nganh Hoc Details Meta Box Callback
 */
function inthub_nganh_hoc_details_callback($post) {
    // Add nonce field for security
    wp_nonce_field('inthub_nganh_hoc_meta_nonce', 'inthub_nganh_hoc_meta_nonce_field');

    // Get current values
    $ma_nganh = get_post_meta($post->ID, 'ma_nganh', true);
    $ten_nganh_tieng_anh = get_post_meta($post->ID, 'ten_nganh_tieng_anh', true);
    $mo_ta_ngan = get_post_meta($post->ID, 'mo_ta_ngan', true);
    $mo_ta_chi_tiet = get_post_meta($post->ID, 'mo_ta_chi_tiet', true);
    $co_hoi_nghe_nghiep = get_post_meta($post->ID, 'co_hoi_nghe_nghiep', true);
    $muc_luong_trung_binh = get_post_meta($post->ID, 'muc_luong_trung_binh', true);
    $yeu_cau_dau_vao = get_post_meta($post->ID, 'yeu_cau_dau_vao', true);
    $thoi_gian_dao_tao = get_post_meta($post->ID, 'thoi_gian_dao_tao', true);
    $chuong_trinh_hoc = get_post_meta($post->ID, 'chuong_trinh_hoc', true);
    $ky_nang_can_thiet = get_post_meta($post->ID, 'ky_nang_can_thiet', true);
    $truong_lien_ket = get_post_meta($post->ID, 'truong_lien_ket', true);
    $hoc_bong_lien_quan = get_post_meta($post->ID, 'hoc_bong_lien_quan', true);
    ?>

    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="ma_nganh"><?php _e('Mã ngành', 'inthub'); ?></label>
            </th>
            <td>
                <input type="text"
                       id="ma_nganh"
                       name="ma_nganh"
                       value="<?php echo esc_attr($ma_nganh); ?>"
                       style="width: 200px;"
                       placeholder="<?php _e('Ví dụ: IT001', 'inthub'); ?>" />
                <p class="description"><?php _e('Mã ngành theo quy định của Bộ Giáo dục', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="ten_nganh_tieng_anh"><?php _e('Tên ngành (Tiếng Anh)', 'inthub'); ?></label>
            </th>
            <td>
                <input type="text"
                       id="ten_nganh_tieng_anh"
                       name="ten_nganh_tieng_anh"
                       value="<?php echo esc_attr($ten_nganh_tieng_anh); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: Computer Science', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="thoi_gian_dao_tao"><?php _e('Thời gian đào tạo', 'inthub'); ?></label>
            </th>
            <td>
                <select id="thoi_gian_dao_tao" name="thoi_gian_dao_tao" style="width: 300px;">
                    <option value=""><?php _e('Chọn thời gian đào tạo', 'inthub'); ?></option>
                    <option value="2-nam" <?php selected($thoi_gian_dao_tao, '2-nam'); ?>><?php _e('2 năm', 'inthub'); ?></option>
                    <option value="3-nam" <?php selected($thoi_gian_dao_tao, '3-nam'); ?>><?php _e('3 năm', 'inthub'); ?></option>
                    <option value="4-nam" <?php selected($thoi_gian_dao_tao, '4-nam'); ?>><?php _e('4 năm', 'inthub'); ?></option>
                    <option value="5-nam" <?php selected($thoi_gian_dao_tao, '5-nam'); ?>><?php _e('5 năm', 'inthub'); ?></option>
                    <option value="6-nam" <?php selected($thoi_gian_dao_tao, '6-nam'); ?>><?php _e('6 năm', 'inthub'); ?></option>
                </select>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="muc_luong_trung_binh"><?php _e('Mức lương trung bình', 'inthub'); ?></label>
            </th>
            <td>
                <input type="text"
                       id="muc_luong_trung_binh"
                       name="muc_luong_trung_binh"
                       value="<?php echo esc_attr($muc_luong_trung_binh); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: 15-25 triệu VND/tháng', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="mo_ta_ngan"><?php _e('Mô tả ngắn', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="mo_ta_ngan"
                          name="mo_ta_ngan"
                          rows="3"
                          style="width: 100%;"
                          maxlength="300"
                          placeholder="<?php _e('Mô tả ngắn gọn về ngành học (tối đa 300 ký tự)', 'inthub'); ?>"><?php echo esc_textarea($mo_ta_ngan); ?></textarea>
                <p class="description">
                    <?php _e('Mô tả ngắn gọn về ngành học', 'inthub'); ?>
                    <span id="char-count-short"><?php echo strlen($mo_ta_ngan); ?>/300</span>
                </p>
            </td>
        </tr>
    </table>

    <h3><?php _e('Thông tin chi tiết', 'inthub'); ?></h3>
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="mo_ta_chi_tiet"><?php _e('Mô tả chi tiết', 'inthub'); ?></label>
            </th>
            <td>
                <?php
                wp_editor($mo_ta_chi_tiet, 'mo_ta_chi_tiet', array(
                    'textarea_name' => 'mo_ta_chi_tiet',
                    'media_buttons' => true,
                    'textarea_rows' => 8,
                    'teeny' => false,
                    'tinymce' => true,
                    'quicktags' => true
                ));
                ?>
                <p class="description"><?php _e('Mô tả chi tiết về ngành học, nội dung đào tạo', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="co_hoi_nghe_nghiep"><?php _e('Cơ hội nghề nghiệp', 'inthub'); ?></label>
            </th>
            <td>
                <?php
                wp_editor($co_hoi_nghe_nghiep, 'co_hoi_nghe_nghiep', array(
                    'textarea_name' => 'co_hoi_nghe_nghiep',
                    'media_buttons' => true,
                    'textarea_rows' => 6,
                    'teeny' => false,
                    'tinymce' => true,
                    'quicktags' => true
                ));
                ?>
                <p class="description"><?php _e('Các cơ hội việc làm sau khi tốt nghiệp', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="yeu_cau_dau_vao"><?php _e('Yêu cầu đầu vào', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="yeu_cau_dau_vao"
                          name="yeu_cau_dau_vao"
                          rows="4"
                          style="width: 100%;"
                          placeholder="<?php _e('Điểm số, chứng chỉ, kinh nghiệm...', 'inthub'); ?>"><?php echo esc_textarea($yeu_cau_dau_vao); ?></textarea>
                <p class="description"><?php _e('Các yêu cầu để được nhận vào ngành học', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="chuong_trinh_hoc"><?php _e('Chương trình học', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="chuong_trinh_hoc"
                          name="chuong_trinh_hoc"
                          rows="6"
                          style="width: 100%;"
                          placeholder="<?php _e('Các môn học chính, học phần...', 'inthub'); ?>"><?php echo esc_textarea($chuong_trinh_hoc); ?></textarea>
                <p class="description"><?php _e('Danh sách các môn học và nội dung chương trình', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="ky_nang_can_thiet"><?php _e('Kỹ năng cần thiết', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="ky_nang_can_thiet"
                          name="ky_nang_can_thiet"
                          rows="4"
                          style="width: 100%;"
                          placeholder="<?php _e('Kỹ năng mềm, kỹ năng chuyên môn...', 'inthub'); ?>"><?php echo esc_textarea($ky_nang_can_thiet); ?></textarea>
                <p class="description"><?php _e('Các kỹ năng cần thiết để thành công trong ngành', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="truong_lien_ket"><?php _e('Trường liên kết', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="truong_lien_ket"
                          name="truong_lien_ket"
                          rows="3"
                          style="width: 100%;"
                          placeholder="<?php _e('Danh sách các trường đào tạo ngành này...', 'inthub'); ?>"><?php echo esc_textarea($truong_lien_ket); ?></textarea>
                <p class="description"><?php _e('Các trường đại học có đào tạo ngành này', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="hoc_bong_lien_quan"><?php _e('Học bổng liên quan', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="hoc_bong_lien_quan"
                          name="hoc_bong_lien_quan"
                          rows="3"
                          style="width: 100%;"
                          placeholder="<?php _e('Danh sách học bổng dành cho ngành này...', 'inthub'); ?>"><?php echo esc_textarea($hoc_bong_lien_quan); ?></textarea>
                <p class="description"><?php _e('Các học bổng có thể ứng tuyển cho ngành này', 'inthub'); ?></p>
            </td>
        </tr>
    </table>

    <script>
    jQuery(document).ready(function($) {
        // Character counter for short description
        $('#mo_ta_ngan').on('input', function() {
            var length = $(this).val().length;
            $('#char-count-short').text(length + '/300');

            if (length > 300) {
                $('#char-count-short').css('color', 'red');
            } else {
                $('#char-count-short').css('color', 'inherit');
            }
        });
    });
    </script>

    <?php
}

/**
 * Save Nganh Hoc Meta Data
 */
function inthub_save_nganh_hoc_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['inthub_nganh_hoc_meta_nonce_field']) ||
        !wp_verify_nonce($_POST['inthub_nganh_hoc_meta_nonce_field'], 'inthub_nganh_hoc_meta_nonce')) {
        return;
    }

    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Save meta fields
    $fields = array(
        'ma_nganh',
        'ten_nganh_tieng_anh',
        'mo_ta_ngan',
        'mo_ta_chi_tiet',
        'co_hoi_nghe_nghiep',
        'muc_luong_trung_binh',
        'yeu_cau_dau_vao',
        'thoi_gian_dao_tao',
        'chuong_trinh_hoc',
        'ky_nang_can_thiet',
        'truong_lien_ket',
        'hoc_bong_lien_quan'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = $_POST[$field];

            // Sanitize based on field type
            switch ($field) {
                case 'mo_ta_chi_tiet':
                case 'co_hoi_nghe_nghiep':
                    $value = wp_kses_post($value);
                    break;
                case 'mo_ta_ngan':
                    $value = substr(sanitize_textarea_field($value), 0, 300);
                    break;
                case 'yeu_cau_dau_vao':
                case 'chuong_trinh_hoc':
                case 'ky_nang_can_thiet':
                case 'truong_lien_ket':
                case 'hoc_bong_lien_quan':
                    $value = sanitize_textarea_field($value);
                    break;
                default:
                    $value = sanitize_text_field($value);
                    break;
            }

            update_post_meta($post_id, $field, $value);
        }
    }
}
add_action('save_post', 'inthub_save_nganh_hoc_meta');
