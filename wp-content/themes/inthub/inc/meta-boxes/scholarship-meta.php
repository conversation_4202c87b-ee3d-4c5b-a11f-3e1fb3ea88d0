<?php
/**
 * Scholarship Meta Boxes
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Scholarship Meta Boxes
 */
function inthub_add_scholarship_meta_boxes() {
    add_meta_box(
        'scholarship_details',
        __('<PERSON> tiết học bổng', 'inthub'),
        'inthub_scholarship_details_callback',
        'scholarship',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'inthub_add_scholarship_meta_boxes');

/**
 * Scholarship Details Meta Box Callback
 */
function inthub_scholarship_details_callback($post) {
    // Add nonce field for security
    wp_nonce_field('inthub_scholarship_meta_nonce', 'inthub_scholarship_meta_nonce_field');

    // Get current values
    $scholarship_name = get_post_meta($post->ID, 'scholarship_name', true);
    $scholarship_value = get_post_meta($post->ID, 'scholarship_value', true);
    $deadline = get_post_meta($post->ID, 'deadline', true);
    $eligibility = get_post_meta($post->ID, 'eligibility', true);
    $application_process = get_post_meta($post->ID, 'application_process', true);
    $provider = get_post_meta($post->ID, 'provider', true);
    $country = get_post_meta($post->ID, 'country', true);
    $level = get_post_meta($post->ID, 'level', true);
    $field_of_study = get_post_meta($post->ID, 'field_of_study', true);
    $website_url = get_post_meta($post->ID, 'website_url', true);
    $contact_info = get_post_meta($post->ID, 'contact_info', true);

    // Convert deadline for input field
    $deadline_formatted = $deadline ? date('Y-m-d', strtotime($deadline)) : '';
    ?>

    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="scholarship_name"><?php _e('Tên học bổng', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="scholarship_name"
                       name="scholarship_name"
                       value="<?php echo esc_attr($scholarship_name); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Nhập tên học bổng', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="provider"><?php _e('Nhà tài trợ', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="provider"
                       name="provider"
                       value="<?php echo esc_attr($provider); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Tên tổ chức/trường cung cấp học bổng', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="scholarship_value"><?php _e('Giá trị học bổng', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="scholarship_value"
                       name="scholarship_value"
                       value="<?php echo esc_attr($scholarship_value); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: $10,000/năm hoặc 100% học phí', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="deadline"><?php _e('Hạn nộp hồ sơ', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="date"
                       id="deadline"
                       name="deadline"
                       value="<?php echo esc_attr($deadline_formatted); ?>"
                       required
                       style="width: 300px;" />
                <p class="description"><?php _e('Chọn ngày hết hạn nộp hồ sơ', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="country"><?php _e('Quốc gia', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <select id="country" name="country" required style="width: 300px;">
                    <option value=""><?php _e('Chọn quốc gia', 'inthub'); ?></option>
                    <option value="usa" <?php selected($country, 'usa'); ?>><?php _e('Hoa Kỳ', 'inthub'); ?></option>
                    <option value="uk" <?php selected($country, 'uk'); ?>><?php _e('Anh', 'inthub'); ?></option>
                    <option value="canada" <?php selected($country, 'canada'); ?>><?php _e('Canada', 'inthub'); ?></option>
                    <option value="australia" <?php selected($country, 'australia'); ?>><?php _e('Úc', 'inthub'); ?></option>
                    <option value="germany" <?php selected($country, 'germany'); ?>><?php _e('Đức', 'inthub'); ?></option>
                    <option value="france" <?php selected($country, 'france'); ?>><?php _e('Pháp', 'inthub'); ?></option>
                    <option value="japan" <?php selected($country, 'japan'); ?>><?php _e('Nhật Bản', 'inthub'); ?></option>
                    <option value="south_korea" <?php selected($country, 'south_korea'); ?>><?php _e('Hàn Quốc', 'inthub'); ?></option>
                    <option value="singapore" <?php selected($country, 'singapore'); ?>><?php _e('Singapore', 'inthub'); ?></option>
                    <option value="netherlands" <?php selected($country, 'netherlands'); ?>><?php _e('Hà Lan', 'inthub'); ?></option>
                    <option value="other" <?php selected($country, 'other'); ?>><?php _e('Khác', 'inthub'); ?></option>
                </select>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="level"><?php _e('Bậc học', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <select id="level" name="level" required style="width: 300px;">
                    <option value=""><?php _e('Chọn bậc học', 'inthub'); ?></option>
                    <option value="undergraduate" <?php selected($level, 'undergraduate'); ?>><?php _e('Đại học', 'inthub'); ?></option>
                    <option value="master" <?php selected($level, 'master'); ?>><?php _e('Thạc sĩ', 'inthub'); ?></option>
                    <option value="phd" <?php selected($level, 'phd'); ?>><?php _e('Tiến sĩ', 'inthub'); ?></option>
                    <option value="postdoc" <?php selected($level, 'postdoc'); ?>><?php _e('Sau tiến sĩ', 'inthub'); ?></option>
                    <option value="all" <?php selected($level, 'all'); ?>><?php _e('Tất cả bậc học', 'inthub'); ?></option>
                </select>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="field_of_study"><?php _e('Lĩnh vực học', 'inthub'); ?></label>
            </th>
            <td>
                <input type="text"
                       id="field_of_study"
                       name="field_of_study"
                       value="<?php echo esc_attr($field_of_study); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: Kỹ thuật, Y học, Kinh tế...', 'inthub'); ?>" />
                <p class="description"><?php _e('Để trống nếu áp dụng cho tất cả lĩnh vực', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="website_url"><?php _e('Website chính thức', 'inthub'); ?></label>
            </th>
            <td>
                <input type="url"
                       id="website_url"
                       name="website_url"
                       value="<?php echo esc_attr($website_url); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('https://example.com/scholarship', 'inthub'); ?>" />
            </td>
        </tr>
    </table>

    <h3><?php _e('Thông tin chi tiết', 'inthub'); ?></h3>
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="eligibility"><?php _e('Điều kiện ứng tuyển', 'inthub'); ?></label>
            </th>
            <td>
                <?php
                wp_editor($eligibility, 'eligibility', array(
                    'textarea_name' => 'eligibility',
                    'media_buttons' => true,
                    'textarea_rows' => 6,
                    'teeny' => false,
                    'tinymce' => true,
                    'quicktags' => true
                ));
                ?>
                <p class="description"><?php _e('Mô tả chi tiết các điều kiện để ứng tuyển học bổng', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="application_process"><?php _e('Quy trình đăng ký', 'inthub'); ?></label>
            </th>
            <td>
                <?php
                wp_editor($application_process, 'application_process', array(
                    'textarea_name' => 'application_process',
                    'media_buttons' => true,
                    'textarea_rows' => 6,
                    'teeny' => false,
                    'tinymce' => true,
                    'quicktags' => true
                ));
                ?>
                <p class="description"><?php _e('Hướng dẫn chi tiết cách thức đăng ký học bổng', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="contact_info"><?php _e('Thông tin liên hệ', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="contact_info"
                          name="contact_info"
                          rows="4"
                          style="width: 100%;"
                          placeholder="<?php _e('Email, điện thoại, địa chỉ liên hệ...', 'inthub'); ?>"><?php echo esc_textarea($contact_info); ?></textarea>
                <p class="description"><?php _e('Thông tin liên hệ để hỗ trợ ứng viên', 'inthub'); ?></p>
            </td>
        </tr>
    </table>

    <?php
}

/**
 * Save Scholarship Meta Data
 */
function inthub_save_scholarship_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['inthub_scholarship_meta_nonce_field']) ||
        !wp_verify_nonce($_POST['inthub_scholarship_meta_nonce_field'], 'inthub_scholarship_meta_nonce')) {
        return;
    }

    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Save meta fields
    $fields = array(
        'scholarship_name',
        'scholarship_value',
        'deadline',
        'eligibility',
        'application_process',
        'provider',
        'country',
        'level',
        'field_of_study',
        'website_url',
        'contact_info'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = $_POST[$field];

            // Sanitize based on field type
            switch ($field) {
                case 'deadline':
                    $value = sanitize_text_field($value);
                    // Convert to MySQL date format
                    if ($value) {
                        $value = date('Y-m-d', strtotime($value));
                    }
                    break;
                case 'website_url':
                    $value = esc_url_raw($value);
                    break;
                case 'eligibility':
                case 'application_process':
                    $value = wp_kses_post($value);
                    break;
                case 'contact_info':
                    $value = sanitize_textarea_field($value);
                    break;
                default:
                    $value = sanitize_text_field($value);
                    break;
            }

            update_post_meta($post_id, $field, $value);
        }
    }
}
add_action('save_post', 'inthub_save_scholarship_meta');
