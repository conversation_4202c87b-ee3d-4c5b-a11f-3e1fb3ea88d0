<?php
/**
 * Enqueue Scripts and Styles
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue Scripts and Styles
 */
function inthub_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('inthub-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue Tailwind CSS from CDN
    wp_enqueue_style('tailwind-css', 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css', array(), '2.2.19');
    
    // Enqueue custom CSS
    wp_enqueue_style('inthub-custom', get_template_directory_uri() . '/assets/css/custom.css', array('inthub-style'), '1.0.0');
    
    // Enqueue main JavaScript
    wp_enqueue_script('inthub-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Enqueue slider JavaScript
    wp_enqueue_script('inthub-slider', get_template_directory_uri() . '/assets/js/slider.js', array('jquery'), '1.0.0', true);
    
    // Enqueue navigation JavaScript
    wp_enqueue_script('inthub-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array('jquery'), '1.0.0', true);

    // Enqueue University CSS and JS on university pages
    if (is_post_type_archive('university') || is_singular('university')) {
        wp_enqueue_style('inthub-university', get_template_directory_uri() . '/assets/css/university.css', array('inthub-custom'), '1.0.0');
        wp_enqueue_script('inthub-university', get_template_directory_uri() . '/assets/js/university.js', array('jquery'), '1.0.0', true);
    }

    // Enqueue Scholarship CSS and JS on scholarship pages
    if (is_post_type_archive('scholarship') || is_singular('scholarship')) {
        wp_enqueue_style('inthub-scholarship', get_template_directory_uri() . '/assets/css/scholarship.css', array('inthub-custom'), '1.0.0');
        wp_enqueue_script('inthub-scholarship', get_template_directory_uri() . '/assets/js/scholarship.js', array('jquery'), '1.0.0', true);
    }

    // Enqueue Nganh Hoc CSS and JS on nganh_hoc pages
    if (is_post_type_archive('nganh_hoc') || is_singular('nganh_hoc') ||
        is_tax('linh_vuc') || is_tax('cap_do_dao_tao')) {
        wp_enqueue_style('inthub-nganh-hoc', get_template_directory_uri() . '/assets/css/nganh-hoc.css', array('inthub-custom'), '1.0.0');
        wp_enqueue_script('inthub-nganh-hoc', get_template_directory_uri() . '/assets/js/nganh-hoc.js', array('jquery'), '1.0.0', true);
    }

    // Localize script for AJAX
    wp_localize_script('inthub-main', 'inthub_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('inthub_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'inthub_scripts');

/**
 * Enqueue Scholarship Frontend Styles
 */
function inthub_enqueue_scholarship_styles() {
    if (is_singular('scholarship')) {
        wp_enqueue_style(
            'inthub-scholarship-frontend',
            get_template_directory_uri() . '/assets/css/scholarship-frontend.css',
            array(),
            '1.0.0'
        );
    }
}
add_action('wp_enqueue_scripts', 'inthub_enqueue_scholarship_styles');

/**
 * Enqueue university taxonomy styles
 */
function inthub_enqueue_university_taxonomy_styles() {
    if (is_tax('university_category') || is_post_type_archive('university') || is_singular('university')) {
        wp_enqueue_style(
            'inthub-university-taxonomy',
            get_template_directory_uri() . '/assets/css/university-taxonomy.css',
            array(),
            '1.0.0'
        );
    }
}
add_action('wp_enqueue_scripts', 'inthub_enqueue_university_taxonomy_styles');

/**
 * Enqueue scholarship taxonomy styles
 */
function inthub_enqueue_scholarship_taxonomy_styles() {
    if (is_tax('scholarship_category') || is_post_type_archive('scholarship') || is_singular('scholarship')) {
        wp_enqueue_style(
            'inthub-scholarship-taxonomy',
            get_template_directory_uri() . '/assets/css/scholarship-taxonomy.css',
            array(),
            '1.0.0'
        );
    }
}
add_action('wp_enqueue_scripts', 'inthub_enqueue_scholarship_taxonomy_styles');

/**
 * Enqueue dropdown navigation styles
 */
function inthub_enqueue_dropdown_navigation_styles() {
    wp_enqueue_style(
        'inthub-dropdown-navigation',
        get_template_directory_uri() . '/assets/css/dropdown-navigation.css',
        array(),
        '1.0.0'
    );
}
add_action('wp_enqueue_scripts', 'inthub_enqueue_dropdown_navigation_styles');
