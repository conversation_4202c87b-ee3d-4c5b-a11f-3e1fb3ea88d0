<?php
/**
 * Navigation and Menu Functions
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enhanced Custom Walker for Multi-Level Navigation Menu
 */
class IntHub_Walker_Nav_Menu extends Walker_Nav_Menu {

    // Track if current item has children
    public $has_children = false;

    /**
     * Starts the list before the elements are added.
     */
    public function start_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $classes = array('sub-menu');

        // Add depth-specific classes
        if ($depth == 0) {
            $classes[] = 'dropdown-menu';
        } else {
            $classes[] = 'dropdown-submenu';
            $classes[] = 'dropdown-menu-level-' . ($depth + 1);
        }

        $class_names = join(' ', apply_filters('nav_menu_submenu_css_class', $classes, $args, $depth));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $output .= "\n$indent<ul$class_names>\n";
    }

    /**
     * Ends the list after the elements are added.
     */
    public function end_lvl(&$output, $depth = 0, $args = null) {
        $indent = str_repeat("\t", $depth);
        $output .= "$indent</ul>\n";
    }

    /**
     * Starts the element output.
     */
    public function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $indent = ($depth) ? str_repeat("\t", $depth) : '';

        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;

        // More efficient way to check if this item has children
        // WordPress automatically adds 'menu-item-has-children' class
        $this->has_children = in_array('menu-item-has-children', $classes);

        if ($this->has_children) {
            $classes[] = 'dropdown';

            // Add appropriate submenu classes based on depth
            if ($depth > 0) {
                $classes[] = 'dropdown-submenu';
                $classes[] = 'dropdown-submenu-level-' . $depth;
            }
        }

        // Add depth-specific classes for all items
        $classes[] = 'menu-item-depth-' . $depth;

        // Add positioning classes for deeper levels
        if ($depth >= 2) {
            $classes[] = 'dropdown-deep-level';
        }

        $classes = apply_filters('nav_menu_css_class', array_filter($classes), $item, $args);
        $class_names = join(' ', $classes);
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';

        $output .= $indent . '<li' . $id . $class_names .'>';

        $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
        $attributes .= ! empty($item->target)     ? ' target="' . esc_attr($item->target     ) .'"' : '';
        $attributes .= ! empty($item->xfn)        ? ' rel="'    . esc_attr($item->xfn        ) .'"' : '';
        $attributes .= ! empty($item->url)        ? ' href="'   . esc_attr($item->url        ) .'"' : '';

        // Add ARIA attributes for accessibility
        if ($this->has_children) {
            $attributes .= ' aria-haspopup="true" aria-expanded="false"';
        }

        // Build link classes based on depth
        $link_classes = array('nav-link');
        if ($depth == 0) {
            $link_classes[] = 'text-blue-900 font-medium hover:text-pink-500 transition duration-300';
        } else {
            $link_classes[] = 'dropdown-item text-gray-700 hover:text-pink-500 hover:bg-gray-50 transition duration-200';
        }

        $link_class_names = join(' ', $link_classes);

        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a' . $attributes . ' class="' . esc_attr($link_class_names) . '">';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');

        // Add dropdown arrow for items with children
        if ($this->has_children) {
            $arrow_class = $depth == 0 ? 'dropdown-arrow' : 'dropdown-arrow-sub';
            $item_output .= ' <span class="' . $arrow_class . ' ml-1">';
            $item_output .= '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
            if ($depth == 0) {
                $item_output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
            } else {
                $item_output .= '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>';
            }
            $item_output .= '</svg></span>';
        }

        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';

        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }

    /**
     * Ends the element output.
     */
    public function end_el(&$output, $item, $depth = 0, $args = null) {
        $output .= "</li>\n";
    }
}
