<?php
/**
 * Widget Areas and Custom Widgets
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Widget Areas
 */
function inthub_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'inthub'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your sidebar.', 'inthub'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 1', 'inthub'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in the first footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 2', 'inthub'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in the second footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 3', 'inthub'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in the third footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 4', 'inthub'),
        'id'            => 'footer-4',
        'description'   => __('Add widgets here to appear in the fourth footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'inthub_widgets_init');

/**
 * University Categories Widget
 */
class IntHub_University_Categories_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'inthub_university_categories',
            __('Danh mục trường đại học', 'inthub'),
            array(
                'description' => __('Hiển thị danh mục trường đại học', 'inthub'),
                'classname' => 'widget_university_categories'
            )
        );
    }

    public function widget($args, $instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Danh mục trường', 'inthub');
        $show_count = !empty($instance['show_count']) ? $instance['show_count'] : false;
        $hierarchical = !empty($instance['hierarchical']) ? $instance['hierarchical'] : true;

        echo $args['before_widget'];

        if ($title) {
            echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];
        }

        $categories = get_terms(array(
            'taxonomy' => 'university_category',
            'hide_empty' => true,
            'parent' => $hierarchical ? 0 : '',
        ));

        if ($categories && !is_wp_error($categories)) {
            echo '<ul class="university-categories-list">';

            foreach ($categories as $category) {
                $count_text = $show_count ? ' (' . $category->count . ')' : '';
                echo '<li class="category-item">';
                echo '<a href="' . get_term_link($category) . '" class="category-link">';
                echo esc_html($category->name) . $count_text;
                echo '</a>';

                // Show child categories if hierarchical
                if ($hierarchical) {
                    $child_categories = get_terms(array(
                        'taxonomy' => 'university_category',
                        'hide_empty' => true,
                        'parent' => $category->term_id,
                    ));

                    if ($child_categories && !is_wp_error($child_categories)) {
                        echo '<ul class="child-categories">';
                        foreach ($child_categories as $child) {
                            $child_count_text = $show_count ? ' (' . $child->count . ')' : '';
                            echo '<li class="child-category-item">';
                            echo '<a href="' . get_term_link($child) . '" class="child-category-link">';
                            echo esc_html($child->name) . $child_count_text;
                            echo '</a>';
                            echo '</li>';
                        }
                        echo '</ul>';
                    }
                }

                echo '</li>';
            }

            echo '</ul>';
        } else {
            echo '<p>' . __('Chưa có danh mục nào.', 'inthub') . '</p>';
        }

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Danh mục trường', 'inthub');
        $show_count = !empty($instance['show_count']) ? $instance['show_count'] : false;
        $hierarchical = !empty($instance['hierarchical']) ? $instance['hierarchical'] : true;
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Tiêu đề:', 'inthub'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_count); ?>
                   id="<?php echo esc_attr($this->get_field_id('show_count')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('show_count')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('show_count')); ?>"><?php _e('Hiển thị số lượng', 'inthub'); ?></label>
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($hierarchical); ?>
                   id="<?php echo esc_attr($this->get_field_id('hierarchical')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('hierarchical')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('hierarchical')); ?>"><?php _e('Hiển thị phân cấp', 'inthub'); ?></label>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_count'] = (!empty($new_instance['show_count'])) ? 1 : 0;
        $instance['hierarchical'] = (!empty($new_instance['hierarchical'])) ? 1 : 0;
        return $instance;
    }
}

/**
 * Scholarship Categories Widget
 */
class IntHub_Scholarship_Categories_Widget extends WP_Widget {

    public function __construct() {
        parent::__construct(
            'inthub_scholarship_categories',
            __('Danh mục học bổng', 'inthub'),
            array(
                'description' => __('Hiển thị danh mục học bổng', 'inthub'),
                'classname' => 'widget_scholarship_categories'
            )
        );
    }

    public function widget($args, $instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Danh mục học bổng', 'inthub');
        $show_count = !empty($instance['show_count']) ? $instance['show_count'] : false;
        $hierarchical = !empty($instance['hierarchical']) ? $instance['hierarchical'] : true;

        echo $args['before_widget'];

        if ($title) {
            echo $args['before_title'] . apply_filters('widget_title', $title) . $args['after_title'];
        }

        $categories = get_terms(array(
            'taxonomy' => 'scholarship_category',
            'hide_empty' => true,
            'parent' => $hierarchical ? 0 : '',
        ));

        if ($categories && !is_wp_error($categories)) {
            echo '<ul class="scholarship-categories-list">';

            foreach ($categories as $category) {
                $count_text = $show_count ? ' (' . $category->count . ')' : '';
                echo '<li class="category-item">';
                echo '<a href="' . get_term_link($category) . '" class="category-link">';
                echo esc_html($category->name) . $count_text;
                echo '</a>';

                // Show child categories if hierarchical
                if ($hierarchical) {
                    $child_categories = get_terms(array(
                        'taxonomy' => 'scholarship_category',
                        'hide_empty' => true,
                        'parent' => $category->term_id,
                    ));

                    if ($child_categories && !is_wp_error($child_categories)) {
                        echo '<ul class="child-categories">';
                        foreach ($child_categories as $child) {
                            $child_count_text = $show_count ? ' (' . $child->count . ')' : '';
                            echo '<li class="child-category-item">';
                            echo '<a href="' . get_term_link($child) . '" class="child-category-link">';
                            echo esc_html($child->name) . $child_count_text;
                            echo '</a>';
                            echo '</li>';
                        }
                        echo '</ul>';
                    }
                }

                echo '</li>';
            }

            echo '</ul>';
        } else {
            echo '<p>' . __('Chưa có danh mục nào.', 'inthub') . '</p>';
        }

        echo $args['after_widget'];
    }

    public function form($instance) {
        $title = !empty($instance['title']) ? $instance['title'] : __('Danh mục học bổng', 'inthub');
        $show_count = !empty($instance['show_count']) ? $instance['show_count'] : false;
        $hierarchical = !empty($instance['hierarchical']) ? $instance['hierarchical'] : true;
        ?>
        <p>
            <label for="<?php echo esc_attr($this->get_field_id('title')); ?>"><?php _e('Tiêu đề:', 'inthub'); ?></label>
            <input class="widefat" id="<?php echo esc_attr($this->get_field_id('title')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('title')); ?>" type="text"
                   value="<?php echo esc_attr($title); ?>">
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($show_count); ?>
                   id="<?php echo esc_attr($this->get_field_id('show_count')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('show_count')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('show_count')); ?>"><?php _e('Hiển thị số lượng', 'inthub'); ?></label>
        </p>
        <p>
            <input class="checkbox" type="checkbox" <?php checked($hierarchical); ?>
                   id="<?php echo esc_attr($this->get_field_id('hierarchical')); ?>"
                   name="<?php echo esc_attr($this->get_field_name('hierarchical')); ?>">
            <label for="<?php echo esc_attr($this->get_field_id('hierarchical')); ?>"><?php _e('Hiển thị phân cấp', 'inthub'); ?></label>
        </p>
        <?php
    }

    public function update($new_instance, $old_instance) {
        $instance = array();
        $instance['title'] = (!empty($new_instance['title'])) ? sanitize_text_field($new_instance['title']) : '';
        $instance['show_count'] = (!empty($new_instance['show_count'])) ? 1 : 0;
        $instance['hierarchical'] = (!empty($new_instance['hierarchical'])) ? 1 : 0;
        return $instance;
    }
}

// Register the widgets
function inthub_register_taxonomy_widgets() {
    register_widget('IntHub_University_Categories_Widget');
    register_widget('IntHub_Scholarship_Categories_Widget');
}
add_action('widgets_init', 'inthub_register_taxonomy_widgets');
