<?php
/**
 * Mega Menu Custom Fields
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class IntHub_Mega_Menu_Fields {
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('wp_nav_menu_item_custom_fields', array($this, 'add_custom_fields'), 10, 4);
        add_action('wp_update_nav_menu_item', array($this, 'save_custom_fields'), 10, 3);
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Add custom fields to menu items
     */
    public function add_custom_fields($item_id, $item, $depth, $args) {
        $mega_menu_enabled = get_post_meta($item_id, '_mega_menu_enabled', true);
        $mega_menu_image = get_post_meta($item_id, '_mega_menu_image', true);
        $mega_menu_description = get_post_meta($item_id, '_mega_menu_description', true);
        $mega_menu_columns = get_post_meta($item_id, '_mega_menu_columns', true);
        $mega_menu_featured = get_post_meta($item_id, '_mega_menu_featured', true);
        ?>
        
        <div class="mega-menu-fields" style="margin-top: 10px; padding: 10px; border: 1px solid #ddd; background: #f9f9f9;">
            <h4 style="margin: 0 0 10px 0; color: #333;">Mega Menu Settings</h4>
            
            <!-- Enable Mega Menu -->
            <p class="field-mega-menu-enabled description description-wide">
                <label for="edit-menu-item-mega-menu-enabled-<?php echo $item_id; ?>">
                    <input type="checkbox" 
                           id="edit-menu-item-mega-menu-enabled-<?php echo $item_id; ?>" 
                           name="menu-item-mega-menu-enabled[<?php echo $item_id; ?>]" 
                           value="1" 
                           <?php checked($mega_menu_enabled, 1); ?> />
                    Enable Mega Menu
                </label>
            </p>
            
            <div class="mega-menu-options" style="<?php echo $mega_menu_enabled ? '' : 'display: none;'; ?>">
                
                <!-- Mega Menu Image -->
                <p class="field-mega-menu-image description description-wide">
                    <label for="edit-menu-item-mega-menu-image-<?php echo $item_id; ?>">
                        Mega Menu Image URL<br />
                        <input type="url" 
                               id="edit-menu-item-mega-menu-image-<?php echo $item_id; ?>" 
                               class="widefat edit-menu-item-mega-menu-image" 
                               name="menu-item-mega-menu-image[<?php echo $item_id; ?>]" 
                               value="<?php echo esc_attr($mega_menu_image); ?>" />
                    </label>
                    <button type="button" class="button mega-menu-image-upload" data-item-id="<?php echo $item_id; ?>">
                        Upload Image
                    </button>
                    <?php if ($mega_menu_image): ?>
                        <div class="mega-menu-image-preview" style="margin-top: 10px;">
                            <img src="<?php echo esc_url($mega_menu_image); ?>" style="max-width: 100px; height: auto;" />
                        </div>
                    <?php endif; ?>
                </p>
                
                <!-- Mega Menu Description -->
                <p class="field-mega-menu-description description description-wide">
                    <label for="edit-menu-item-mega-menu-description-<?php echo $item_id; ?>">
                        Mega Menu Description<br />
                        <textarea id="edit-menu-item-mega-menu-description-<?php echo $item_id; ?>" 
                                  class="widefat edit-menu-item-mega-menu-description" 
                                  rows="3" 
                                  name="menu-item-mega-menu-description[<?php echo $item_id; ?>]"><?php echo esc_textarea($mega_menu_description); ?></textarea>
                    </label>
                </p>
                
                <!-- Mega Menu Columns -->
                <p class="field-mega-menu-columns description description-wide">
                    <label for="edit-menu-item-mega-menu-columns-<?php echo $item_id; ?>">
                        Number of Columns<br />
                        <select id="edit-menu-item-mega-menu-columns-<?php echo $item_id; ?>" 
                                class="widefat edit-menu-item-mega-menu-columns" 
                                name="menu-item-mega-menu-columns[<?php echo $item_id; ?>]">
                            <option value="auto" <?php selected($mega_menu_columns, 'auto'); ?>>Auto</option>
                            <option value="2" <?php selected($mega_menu_columns, '2'); ?>>2 Columns</option>
                            <option value="3" <?php selected($mega_menu_columns, '3'); ?>>3 Columns</option>
                            <option value="4" <?php selected($mega_menu_columns, '4'); ?>>4 Columns</option>
                        </select>
                    </label>
                </p>
                
                <!-- Featured Item -->
                <p class="field-mega-menu-featured description description-wide">
                    <label for="edit-menu-item-mega-menu-featured-<?php echo $item_id; ?>">
                        <input type="checkbox" 
                               id="edit-menu-item-mega-menu-featured-<?php echo $item_id; ?>" 
                               name="menu-item-mega-menu-featured[<?php echo $item_id; ?>]" 
                               value="1" 
                               <?php checked($mega_menu_featured, 1); ?> />
                        Featured Item (takes more space)
                    </label>
                </p>
                
            </div>
        </div>
        
        <script>
        jQuery(document).ready(function($) {
            // Toggle mega menu options
            $('#edit-menu-item-mega-menu-enabled-<?php echo $item_id; ?>').change(function() {
                var $options = $(this).closest('.mega-menu-fields').find('.mega-menu-options');
                if ($(this).is(':checked')) {
                    $options.show();
                } else {
                    $options.hide();
                }
            });
        });
        </script>
        
        <?php
    }
    
    /**
     * Save custom fields
     */
    public function save_custom_fields($menu_id, $menu_item_db_id, $args) {
        // Save mega menu enabled
        if (isset($_POST['menu-item-mega-menu-enabled'][$menu_item_db_id])) {
            update_post_meta($menu_item_db_id, '_mega_menu_enabled', 1);
        } else {
            delete_post_meta($menu_item_db_id, '_mega_menu_enabled');
        }
        
        // Save mega menu image
        if (isset($_POST['menu-item-mega-menu-image'][$menu_item_db_id])) {
            $image_url = sanitize_url($_POST['menu-item-mega-menu-image'][$menu_item_db_id]);
            update_post_meta($menu_item_db_id, '_mega_menu_image', $image_url);
        }
        
        // Save mega menu description
        if (isset($_POST['menu-item-mega-menu-description'][$menu_item_db_id])) {
            $description = sanitize_textarea_field($_POST['menu-item-mega-menu-description'][$menu_item_db_id]);
            update_post_meta($menu_item_db_id, '_mega_menu_description', $description);
        }
        
        // Save mega menu columns
        if (isset($_POST['menu-item-mega-menu-columns'][$menu_item_db_id])) {
            $columns = sanitize_text_field($_POST['menu-item-mega-menu-columns'][$menu_item_db_id]);
            update_post_meta($menu_item_db_id, '_mega_menu_columns', $columns);
        }
        
        // Save featured status
        if (isset($_POST['menu-item-mega-menu-featured'][$menu_item_db_id])) {
            update_post_meta($menu_item_db_id, '_mega_menu_featured', 1);
        } else {
            delete_post_meta($menu_item_db_id, '_mega_menu_featured');
        }
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        if ('nav-menus.php' !== $hook) {
            return;
        }
        
        wp_enqueue_media();
        wp_enqueue_script(
            'mega-menu-admin',
            get_template_directory_uri() . '/assets/js/mega-menu-admin.js',
            array('jquery'),
            '1.0.0',
            true
        );
    }
}

// Initialize the class
new IntHub_Mega_Menu_Fields();

/**
 * Helper function to get mega menu data
 */
function inthub_get_mega_menu_data($item_id) {
    return array(
        'enabled' => get_post_meta($item_id, '_mega_menu_enabled', true),
        'image' => get_post_meta($item_id, '_mega_menu_image', true),
        'description' => get_post_meta($item_id, '_mega_menu_description', true),
        'columns' => get_post_meta($item_id, '_mega_menu_columns', true),
        'featured' => get_post_meta($item_id, '_mega_menu_featured', true),
    );
}
