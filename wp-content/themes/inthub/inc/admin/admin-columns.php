<?php
/**
 * Admin Columns Customization
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Custom Columns for Event Post Type
 */
function inthub_event_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['event_start_date'] = __('Ngày bắt đầu', 'inthub');
    $new_columns['event_end_date'] = __('Ngày kết thúc', 'inthub');
    $new_columns['event_location'] = __('Địa điểm', 'inthub');
    $new_columns['event_status'] = __('Trạng thái', 'inthub');
    $new_columns['date'] = $columns['date'];
    
    return $new_columns;
}
add_filter('manage_event_posts_columns', 'inthub_event_admin_columns');

/**
 * Populate Custom Columns for Event Post Type
 */
function inthub_event_admin_columns_content($column, $post_id) {
    switch ($column) {
        case 'event_start_date':
            $start_date = get_post_meta($post_id, 'event_start_datetime', true);
            if ($start_date) {
                echo date('d/m/Y H:i', strtotime($start_date));
            } else {
                echo '—';
            }
            break;
            
        case 'event_end_date':
            $end_date = get_post_meta($post_id, 'event_end_datetime', true);
            if ($end_date) {
                echo date('d/m/Y H:i', strtotime($end_date));
            } else {
                echo '—';
            }
            break;
            
        case 'event_location':
            $location = get_post_meta($post_id, 'event_location', true);
            echo $location ? esc_html($location) : '—';
            break;
            
        case 'event_status':
            $start_date = get_post_meta($post_id, 'event_start_datetime', true);
            $end_date = get_post_meta($post_id, 'event_end_datetime', true);
            $now = current_time('mysql');
            
            if ($start_date && $end_date) {
                if ($now < $start_date) {
                    echo '<span style="color: #0073aa; font-weight: bold;">' . __('Sắp diễn ra', 'inthub') . '</span>';
                } elseif ($now >= $start_date && $now <= $end_date) {
                    echo '<span style="color: #00a32a; font-weight: bold;">' . __('Đang diễn ra', 'inthub') . '</span>';
                } else {
                    echo '<span style="color: #646970;">' . __('Đã kết thúc', 'inthub') . '</span>';
                }
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_event_posts_custom_column', 'inthub_event_admin_columns_content', 10, 2);

/**
 * Make Event Columns Sortable
 */
function inthub_event_sortable_columns($columns) {
    $columns['event_start_date'] = 'event_start_datetime';
    $columns['event_end_date'] = 'event_end_datetime';
    return $columns;
}
add_filter('manage_edit-event_sortable_columns', 'inthub_event_sortable_columns');

/**
 * Add Custom Columns for University Post Type
 */
function inthub_university_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['university_country'] = __('Quốc gia', 'inthub');
    $new_columns['university_location'] = __('Địa điểm', 'inthub');
    $new_columns['university_categories'] = __('Danh mục', 'inthub');
    $new_columns['date'] = $columns['date'];
    
    return $new_columns;
}
add_filter('manage_university_posts_columns', 'inthub_university_admin_columns');

/**
 * Populate Custom Columns for University Post Type
 */
function inthub_university_admin_columns_content($column, $post_id) {
    switch ($column) {
        case 'university_country':
            $country = get_post_meta($post_id, 'country', true);
            $countries = array(
                'usa' => 'Hoa Kỳ',
                'uk' => 'Anh',
                'canada' => 'Canada',
                'australia' => 'Úc',
                'germany' => 'Đức',
                'france' => 'Pháp',
                'japan' => 'Nhật Bản',
                'south_korea' => 'Hàn Quốc',
                'singapore' => 'Singapore',
                'netherlands' => 'Hà Lan',
                'other' => 'Khác'
            );
            echo isset($countries[$country]) ? $countries[$country] : '—';
            break;
            
        case 'university_location':
            $location = get_post_meta($post_id, 'location', true);
            echo $location ? esc_html($location) : '—';
            break;
            
        case 'university_categories':
            $terms = get_the_terms($post_id, 'university_category');
            if ($terms && !is_wp_error($terms)) {
                $term_names = array();
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                }
                echo implode(', ', $term_names);
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_university_posts_custom_column', 'inthub_university_admin_columns_content', 10, 2);

/**
 * Add Custom Columns for Scholarship Post Type
 */
function inthub_scholarship_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['scholarship_provider'] = __('Nhà tài trợ', 'inthub');
    $new_columns['scholarship_value'] = __('Giá trị', 'inthub');
    $new_columns['scholarship_deadline'] = __('Hạn nộp', 'inthub');
    $new_columns['scholarship_country'] = __('Quốc gia', 'inthub');
    $new_columns['scholarship_level'] = __('Bậc học', 'inthub');
    $new_columns['scholarship_categories'] = __('Danh mục', 'inthub');
    $new_columns['date'] = $columns['date'];
    
    return $new_columns;
}
add_filter('manage_scholarship_posts_columns', 'inthub_scholarship_admin_columns');

/**
 * Populate Custom Columns for Scholarship Post Type
 */
function inthub_scholarship_admin_columns_content($column, $post_id) {
    switch ($column) {
        case 'scholarship_provider':
            $provider = get_post_meta($post_id, 'provider', true);
            echo $provider ? esc_html($provider) : '—';
            break;
            
        case 'scholarship_value':
            $value = get_post_meta($post_id, 'scholarship_value', true);
            echo $value ? esc_html($value) : '—';
            break;
            
        case 'scholarship_deadline':
            $deadline = get_post_meta($post_id, 'deadline', true);
            if ($deadline) {
                $deadline_date = date('d/m/Y', strtotime($deadline));
                $now = current_time('timestamp');
                $deadline_timestamp = strtotime($deadline);
                
                if ($deadline_timestamp < $now) {
                    echo '<span style="color: #d63638;">' . $deadline_date . ' (Hết hạn)</span>';
                } elseif ($deadline_timestamp < ($now + 30 * 24 * 60 * 60)) {
                    echo '<span style="color: #dba617;">' . $deadline_date . ' (Sắp hết hạn)</span>';
                } else {
                    echo '<span style="color: #00a32a;">' . $deadline_date . '</span>';
                }
            } else {
                echo '—';
            }
            break;
            
        case 'scholarship_country':
            $country = get_post_meta($post_id, 'country', true);
            $countries = array(
                'usa' => 'Hoa Kỳ',
                'uk' => 'Anh',
                'canada' => 'Canada',
                'australia' => 'Úc',
                'germany' => 'Đức',
                'france' => 'Pháp',
                'japan' => 'Nhật Bản',
                'south_korea' => 'Hàn Quốc',
                'singapore' => 'Singapore',
                'netherlands' => 'Hà Lan',
                'other' => 'Khác'
            );
            echo isset($countries[$country]) ? $countries[$country] : '—';
            break;
            
        case 'scholarship_level':
            $level = get_post_meta($post_id, 'level', true);
            $levels = array(
                'undergraduate' => 'Đại học',
                'master' => 'Thạc sĩ',
                'phd' => 'Tiến sĩ',
                'postdoc' => 'Sau tiến sĩ',
                'all' => 'Tất cả bậc học'
            );
            echo isset($levels[$level]) ? $levels[$level] : '—';
            break;
            
        case 'scholarship_categories':
            $terms = get_the_terms($post_id, 'scholarship_category');
            if ($terms && !is_wp_error($terms)) {
                $term_names = array();
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                }
                echo implode(', ', $term_names);
            } else {
                echo '—';
            }
            break;
    }
}
add_action('manage_scholarship_posts_custom_column', 'inthub_scholarship_admin_columns_content', 10, 2);

/**
 * Make Scholarship Columns Sortable
 */
function inthub_scholarship_sortable_columns($columns) {
    $columns['scholarship_deadline'] = 'deadline';
    $columns['scholarship_provider'] = 'provider';
    return $columns;
}
add_filter('manage_edit-scholarship_sortable_columns', 'inthub_scholarship_sortable_columns');

/**
 * Add Custom Columns for Nganh Hoc Post Type
 */
function inthub_nganh_hoc_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['ma_nganh'] = __('Mã ngành', 'inthub');
    $new_columns['linh_vuc'] = __('Lĩnh vực', 'inthub');
    $new_columns['cap_do_dao_tao'] = __('Cấp độ đào tạo', 'inthub');
    $new_columns['thoi_gian_dao_tao'] = __('Thời gian đào tạo', 'inthub');
    $new_columns['date'] = $columns['date'];

    return $new_columns;
}
add_filter('manage_nganh_hoc_posts_columns', 'inthub_nganh_hoc_admin_columns');

/**
 * Populate Custom Columns for Nganh Hoc Post Type
 */
function inthub_nganh_hoc_admin_columns_content($column, $post_id) {
    switch ($column) {
        case 'ma_nganh':
            $ma_nganh = get_post_meta($post_id, 'ma_nganh', true);
            echo $ma_nganh ? esc_html($ma_nganh) : '—';
            break;

        case 'linh_vuc':
            $terms = get_the_terms($post_id, 'linh_vuc');
            if ($terms && !is_wp_error($terms)) {
                $term_names = array();
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                }
                echo implode(', ', $term_names);
            } else {
                echo '—';
            }
            break;

        case 'cap_do_dao_tao':
            $terms = get_the_terms($post_id, 'cap_do_dao_tao');
            if ($terms && !is_wp_error($terms)) {
                $term_names = array();
                foreach ($terms as $term) {
                    $term_names[] = $term->name;
                }
                echo implode(', ', $term_names);
            } else {
                echo '—';
            }
            break;

        case 'thoi_gian_dao_tao':
            $thoi_gian = get_post_meta($post_id, 'thoi_gian_dao_tao', true);
            $thoi_gian_labels = array(
                '2-nam' => '2 năm',
                '3-nam' => '3 năm',
                '4-nam' => '4 năm',
                '5-nam' => '5 năm',
                '6-nam' => '6 năm'
            );
            echo isset($thoi_gian_labels[$thoi_gian]) ? $thoi_gian_labels[$thoi_gian] : '—';
            break;
    }
}
add_action('manage_nganh_hoc_posts_custom_column', 'inthub_nganh_hoc_admin_columns_content', 10, 2);
