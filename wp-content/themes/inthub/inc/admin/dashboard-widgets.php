<?php
/**
 * Dashboard Widgets
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Custom Dashboard Widgets
 */
function inthub_add_dashboard_widgets() {
    wp_add_dashboard_widget(
        'inthub_content_overview',
        __('Tổng quan nội dung IntHub', 'inthub'),
        'inthub_content_overview_widget'
    );
    
    wp_add_dashboard_widget(
        'inthub_upcoming_events',
        __('Sự kiện sắp tới', 'inthub'),
        'inthub_upcoming_events_widget'
    );
    
    wp_add_dashboard_widget(
        'inthub_scholarship_deadlines',
        __('<PERSON><PERSON><PERSON> bổng sắp hết hạn', 'inthub'),
        'inthub_scholarship_deadlines_widget'
    );
}
add_action('wp_dashboard_setup', 'inthub_add_dashboard_widgets');

/**
 * Content Overview Widget
 */
function inthub_content_overview_widget() {
    // Get post counts
    $universities_count = wp_count_posts('university')->publish;
    $scholarships_count = wp_count_posts('scholarship')->publish;
    $events_count = wp_count_posts('event')->publish;
    $nganh_hoc_count = wp_count_posts('nganh_hoc')->publish;
    $testimonials_count = wp_count_posts('testimonials')->publish;
    $services_count = wp_count_posts('services')->publish;
    
    // Get taxonomy counts
    $university_categories = wp_count_terms('university_category');
    $scholarship_categories = wp_count_terms('scholarship_category');
    $linh_vuc_count = wp_count_terms('linh_vuc');
    $cap_do_count = wp_count_terms('cap_do_dao_tao');
    ?>
    
    <div class="inthub-dashboard-overview">
        <style>
        .inthub-dashboard-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .overview-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border-left: 4px solid #0073aa;
        }
        .overview-item h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
            color: #0073aa;
        }
        .overview-item p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .overview-taxonomies {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
        }
        .taxonomy-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        .taxonomy-item {
            background: #fff;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            text-align: center;
        }
        </style>
        
        <div class="overview-item">
            <h3><?php echo number_format($universities_count); ?></h3>
            <p><?php _e('Trường đại học', 'inthub'); ?></p>
        </div>
        
        <div class="overview-item">
            <h3><?php echo number_format($scholarships_count); ?></h3>
            <p><?php _e('Học bổng', 'inthub'); ?></p>
        </div>
        
        <div class="overview-item">
            <h3><?php echo number_format($events_count); ?></h3>
            <p><?php _e('Sự kiện', 'inthub'); ?></p>
        </div>
        
        <div class="overview-item">
            <h3><?php echo number_format($nganh_hoc_count); ?></h3>
            <p><?php _e('Ngành học', 'inthub'); ?></p>
        </div>
        
        <div class="overview-item">
            <h3><?php echo number_format($testimonials_count); ?></h3>
            <p><?php _e('Testimonials', 'inthub'); ?></p>
        </div>
        
        <div class="overview-item">
            <h3><?php echo number_format($services_count); ?></h3>
            <p><?php _e('Dịch vụ', 'inthub'); ?></p>
        </div>
        
        <div class="overview-taxonomies">
            <h4><?php _e('Danh mục và phân loại', 'inthub'); ?></h4>
            <div class="taxonomy-grid">
                <div class="taxonomy-item">
                    <strong><?php echo number_format($university_categories); ?></strong><br>
                    <small><?php _e('Danh mục trường', 'inthub'); ?></small>
                </div>
                <div class="taxonomy-item">
                    <strong><?php echo number_format($scholarship_categories); ?></strong><br>
                    <small><?php _e('Danh mục học bổng', 'inthub'); ?></small>
                </div>
                <div class="taxonomy-item">
                    <strong><?php echo number_format($linh_vuc_count); ?></strong><br>
                    <small><?php _e('Lĩnh vực', 'inthub'); ?></small>
                </div>
                <div class="taxonomy-item">
                    <strong><?php echo number_format($cap_do_count); ?></strong><br>
                    <small><?php _e('Cấp độ đào tạo', 'inthub'); ?></small>
                </div>
            </div>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <a href="<?php echo admin_url('edit.php?post_type=university'); ?>" class="button"><?php _e('Quản lý trường đại học', 'inthub'); ?></a>
            <a href="<?php echo admin_url('edit.php?post_type=scholarship'); ?>" class="button"><?php _e('Quản lý học bổng', 'inthub'); ?></a>
            <a href="<?php echo admin_url('edit.php?post_type=event'); ?>" class="button"><?php _e('Quản lý sự kiện', 'inthub'); ?></a>
        </div>
    </div>
    
    <?php
}

/**
 * Upcoming Events Widget
 */
function inthub_upcoming_events_widget() {
    $now = current_time('mysql');
    
    $upcoming_events = get_posts(array(
        'post_type' => 'event',
        'posts_per_page' => 5,
        'meta_key' => 'event_start_datetime',
        'orderby' => 'meta_value',
        'order' => 'ASC',
        'meta_query' => array(
            array(
                'key' => 'event_start_datetime',
                'value' => $now,
                'compare' => '>',
                'type' => 'DATETIME'
            )
        )
    ));
    
    if ($upcoming_events) {
        echo '<ul>';
        foreach ($upcoming_events as $event) {
            $start_date = get_post_meta($event->ID, 'event_start_datetime', true);
            $location = get_post_meta($event->ID, 'event_location', true);
            
            echo '<li style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #eee;">';
            echo '<strong><a href="' . get_edit_post_link($event->ID) . '">' . esc_html($event->post_title) . '</a></strong><br>';
            if ($start_date) {
                echo '<small style="color: #666;">' . date('d/m/Y H:i', strtotime($start_date)) . '</small><br>';
            }
            if ($location) {
                echo '<small style="color: #666;">📍 ' . esc_html($location) . '</small>';
            }
            echo '</li>';
        }
        echo '</ul>';
        
        echo '<p style="text-align: center; margin-top: 15px;">';
        echo '<a href="' . admin_url('edit.php?post_type=event') . '" class="button">' . __('Xem tất cả sự kiện', 'inthub') . '</a>';
        echo '</p>';
    } else {
        echo '<p>' . __('Không có sự kiện nào sắp tới.', 'inthub') . '</p>';
        echo '<p style="text-align: center;">';
        echo '<a href="' . admin_url('post-new.php?post_type=event') . '" class="button button-primary">' . __('Tạo sự kiện mới', 'inthub') . '</a>';
        echo '</p>';
    }
}

/**
 * Scholarship Deadlines Widget
 */
function inthub_scholarship_deadlines_widget() {
    $now = current_time('Y-m-d');
    $soon = date('Y-m-d', strtotime('+30 days'));
    
    $expiring_scholarships = get_posts(array(
        'post_type' => 'scholarship',
        'posts_per_page' => 5,
        'meta_key' => 'deadline',
        'orderby' => 'meta_value',
        'order' => 'ASC',
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => 'deadline',
                'value' => $now,
                'compare' => '>=',
                'type' => 'DATE'
            ),
            array(
                'key' => 'deadline',
                'value' => $soon,
                'compare' => '<=',
                'type' => 'DATE'
            )
        )
    ));
    
    if ($expiring_scholarships) {
        echo '<ul>';
        foreach ($expiring_scholarships as $scholarship) {
            $deadline = get_post_meta($scholarship->ID, 'deadline', true);
            $provider = get_post_meta($scholarship->ID, 'provider', true);
            $value = get_post_meta($scholarship->ID, 'scholarship_value', true);
            
            $days_left = floor((strtotime($deadline) - strtotime($now)) / (60 * 60 * 24));
            $urgency_color = $days_left <= 7 ? '#d63638' : ($days_left <= 14 ? '#dba617' : '#00a32a');
            
            echo '<li style="margin-bottom: 10px; padding-bottom: 10px; border-bottom: 1px solid #eee;">';
            echo '<strong><a href="' . get_edit_post_link($scholarship->ID) . '">' . esc_html($scholarship->post_title) . '</a></strong><br>';
            if ($provider) {
                echo '<small style="color: #666;">🏛️ ' . esc_html($provider) . '</small><br>';
            }
            if ($value) {
                echo '<small style="color: #666;">💰 ' . esc_html($value) . '</small><br>';
            }
            if ($deadline) {
                echo '<small style="color: ' . $urgency_color . '; font-weight: bold;">⏰ ' . date('d/m/Y', strtotime($deadline)) . ' (' . $days_left . ' ngày)</small>';
            }
            echo '</li>';
        }
        echo '</ul>';
        
        echo '<p style="text-align: center; margin-top: 15px;">';
        echo '<a href="' . admin_url('edit.php?post_type=scholarship') . '" class="button">' . __('Xem tất cả học bổng', 'inthub') . '</a>';
        echo '</p>';
    } else {
        echo '<p>' . __('Không có học bổng nào sắp hết hạn trong 30 ngày tới.', 'inthub') . '</p>';
        echo '<p style="text-align: center;">';
        echo '<a href="' . admin_url('post-new.php?post_type=scholarship') . '" class="button button-primary">' . __('Tạo học bổng mới', 'inthub') . '</a>';
        echo '</p>';
    }
}
