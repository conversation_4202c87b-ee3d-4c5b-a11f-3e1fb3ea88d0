<?php
/**
 * Admin Filters and Dropdowns
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add Filters for Event Post Type
 */
function inthub_event_admin_filters() {
    global $typenow;
    
    if ($typenow === 'event') {
        // Event Status Filter
        $selected_status = isset($_GET['event_status']) ? $_GET['event_status'] : '';
        ?>
        <select name="event_status">
            <option value=""><?php _e('Tất cả trạng thái', 'inthub'); ?></option>
            <option value="upcoming" <?php selected($selected_status, 'upcoming'); ?>><?php _e('Sắp diễn ra', 'inthub'); ?></option>
            <option value="ongoing" <?php selected($selected_status, 'ongoing'); ?>><?php _e('Đang diễn ra', 'inthub'); ?></option>
            <option value="past" <?php selected($selected_status, 'past'); ?>><?php _e('Đã kết thúc', 'inthub'); ?></option>
        </select>
        <?php
    }
}
add_action('restrict_manage_posts', 'inthub_event_admin_filters');

/**
 * Apply Event Filters
 */
function inthub_event_admin_filter_query($query) {
    global $pagenow, $typenow;
    
    if ($pagenow === 'edit.php' && $typenow === 'event') {
        if (isset($_GET['event_status']) && !empty($_GET['event_status'])) {
            $status = sanitize_text_field($_GET['event_status']);
            $now = current_time('mysql');
            
            $meta_query = array();
            
            switch ($status) {
                case 'upcoming':
                    $meta_query[] = array(
                        'key' => 'event_start_datetime',
                        'value' => $now,
                        'compare' => '>',
                        'type' => 'DATETIME'
                    );
                    break;
                case 'ongoing':
                    $meta_query[] = array(
                        'relation' => 'AND',
                        array(
                            'key' => 'event_start_datetime',
                            'value' => $now,
                            'compare' => '<=',
                            'type' => 'DATETIME'
                        ),
                        array(
                            'key' => 'event_end_datetime',
                            'value' => $now,
                            'compare' => '>=',
                            'type' => 'DATETIME'
                        )
                    );
                    break;
                case 'past':
                    $meta_query[] = array(
                        'key' => 'event_end_datetime',
                        'value' => $now,
                        'compare' => '<',
                        'type' => 'DATETIME'
                    );
                    break;
            }
            
            if (!empty($meta_query)) {
                $query->set('meta_query', $meta_query);
            }
        }
    }
}
add_action('pre_get_posts', 'inthub_event_admin_filter_query');

/**
 * Add Filters for University Post Type
 */
function inthub_university_admin_filters() {
    global $typenow;
    
    if ($typenow === 'university') {
        // Country Filter
        $selected_country = isset($_GET['university_country']) ? $_GET['university_country'] : '';
        ?>
        <select name="university_country">
            <option value=""><?php _e('Tất cả quốc gia', 'inthub'); ?></option>
            <option value="usa" <?php selected($selected_country, 'usa'); ?>><?php _e('Hoa Kỳ', 'inthub'); ?></option>
            <option value="uk" <?php selected($selected_country, 'uk'); ?>><?php _e('Anh', 'inthub'); ?></option>
            <option value="canada" <?php selected($selected_country, 'canada'); ?>><?php _e('Canada', 'inthub'); ?></option>
            <option value="australia" <?php selected($selected_country, 'australia'); ?>><?php _e('Úc', 'inthub'); ?></option>
            <option value="germany" <?php selected($selected_country, 'germany'); ?>><?php _e('Đức', 'inthub'); ?></option>
            <option value="france" <?php selected($selected_country, 'france'); ?>><?php _e('Pháp', 'inthub'); ?></option>
            <option value="japan" <?php selected($selected_country, 'japan'); ?>><?php _e('Nhật Bản', 'inthub'); ?></option>
            <option value="south_korea" <?php selected($selected_country, 'south_korea'); ?>><?php _e('Hàn Quốc', 'inthub'); ?></option>
            <option value="singapore" <?php selected($selected_country, 'singapore'); ?>><?php _e('Singapore', 'inthub'); ?></option>
            <option value="netherlands" <?php selected($selected_country, 'netherlands'); ?>><?php _e('Hà Lan', 'inthub'); ?></option>
            <option value="other" <?php selected($selected_country, 'other'); ?>><?php _e('Khác', 'inthub'); ?></option>
        </select>
        <?php
    }
}
add_action('restrict_manage_posts', 'inthub_university_admin_filters');

/**
 * Apply University Filters
 */
function inthub_university_admin_filter_query($query) {
    global $pagenow, $typenow;
    
    if ($pagenow === 'edit.php' && $typenow === 'university') {
        if (isset($_GET['university_country']) && !empty($_GET['university_country'])) {
            $country = sanitize_text_field($_GET['university_country']);
            $query->set('meta_key', 'country');
            $query->set('meta_value', $country);
        }
    }
}
add_action('pre_get_posts', 'inthub_university_admin_filter_query');

/**
 * Add Filters for Scholarship Post Type
 */
function inthub_scholarship_admin_filters() {
    global $typenow;
    
    if ($typenow === 'scholarship') {
        // Country Filter
        $selected_country = isset($_GET['scholarship_country']) ? $_GET['scholarship_country'] : '';
        ?>
        <select name="scholarship_country">
            <option value=""><?php _e('Tất cả quốc gia', 'inthub'); ?></option>
            <option value="usa" <?php selected($selected_country, 'usa'); ?>><?php _e('Hoa Kỳ', 'inthub'); ?></option>
            <option value="uk" <?php selected($selected_country, 'uk'); ?>><?php _e('Anh', 'inthub'); ?></option>
            <option value="canada" <?php selected($selected_country, 'canada'); ?>><?php _e('Canada', 'inthub'); ?></option>
            <option value="australia" <?php selected($selected_country, 'australia'); ?>><?php _e('Úc', 'inthub'); ?></option>
            <option value="germany" <?php selected($selected_country, 'germany'); ?>><?php _e('Đức', 'inthub'); ?></option>
            <option value="france" <?php selected($selected_country, 'france'); ?>><?php _e('Pháp', 'inthub'); ?></option>
            <option value="japan" <?php selected($selected_country, 'japan'); ?>><?php _e('Nhật Bản', 'inthub'); ?></option>
            <option value="south_korea" <?php selected($selected_country, 'south_korea'); ?>><?php _e('Hàn Quốc', 'inthub'); ?></option>
            <option value="singapore" <?php selected($selected_country, 'singapore'); ?>><?php _e('Singapore', 'inthub'); ?></option>
            <option value="netherlands" <?php selected($selected_country, 'netherlands'); ?>><?php _e('Hà Lan', 'inthub'); ?></option>
            <option value="other" <?php selected($selected_country, 'other'); ?>><?php _e('Khác', 'inthub'); ?></option>
        </select>
        <?php
        
        // Level Filter
        $selected_level = isset($_GET['scholarship_level']) ? $_GET['scholarship_level'] : '';
        ?>
        <select name="scholarship_level">
            <option value=""><?php _e('Tất cả bậc học', 'inthub'); ?></option>
            <option value="undergraduate" <?php selected($selected_level, 'undergraduate'); ?>><?php _e('Đại học', 'inthub'); ?></option>
            <option value="master" <?php selected($selected_level, 'master'); ?>><?php _e('Thạc sĩ', 'inthub'); ?></option>
            <option value="phd" <?php selected($selected_level, 'phd'); ?>><?php _e('Tiến sĩ', 'inthub'); ?></option>
            <option value="postdoc" <?php selected($selected_level, 'postdoc'); ?>><?php _e('Sau tiến sĩ', 'inthub'); ?></option>
            <option value="all" <?php selected($selected_level, 'all'); ?>><?php _e('Tất cả bậc học', 'inthub'); ?></option>
        </select>
        <?php
        
        // Deadline Status Filter
        $selected_deadline = isset($_GET['scholarship_deadline_status']) ? $_GET['scholarship_deadline_status'] : '';
        ?>
        <select name="scholarship_deadline_status">
            <option value=""><?php _e('Tất cả hạn nộp', 'inthub'); ?></option>
            <option value="active" <?php selected($selected_deadline, 'active'); ?>><?php _e('Còn hạn', 'inthub'); ?></option>
            <option value="expiring_soon" <?php selected($selected_deadline, 'expiring_soon'); ?>><?php _e('Sắp hết hạn', 'inthub'); ?></option>
            <option value="expired" <?php selected($selected_deadline, 'expired'); ?>><?php _e('Hết hạn', 'inthub'); ?></option>
        </select>
        <?php
    }
}
add_action('restrict_manage_posts', 'inthub_scholarship_admin_filters');

/**
 * Apply Scholarship Filters
 */
function inthub_scholarship_admin_filter_query($query) {
    global $pagenow, $typenow;
    
    if ($pagenow === 'edit.php' && $typenow === 'scholarship') {
        $meta_query = array();
        
        // Country filter
        if (isset($_GET['scholarship_country']) && !empty($_GET['scholarship_country'])) {
            $country = sanitize_text_field($_GET['scholarship_country']);
            $meta_query[] = array(
                'key' => 'country',
                'value' => $country,
                'compare' => '='
            );
        }
        
        // Level filter
        if (isset($_GET['scholarship_level']) && !empty($_GET['scholarship_level'])) {
            $level = sanitize_text_field($_GET['scholarship_level']);
            $meta_query[] = array(
                'key' => 'level',
                'value' => $level,
                'compare' => '='
            );
        }
        
        // Deadline status filter
        if (isset($_GET['scholarship_deadline_status']) && !empty($_GET['scholarship_deadline_status'])) {
            $deadline_status = sanitize_text_field($_GET['scholarship_deadline_status']);
            $now = current_time('Y-m-d');
            $soon = date('Y-m-d', strtotime('+30 days'));
            
            switch ($deadline_status) {
                case 'active':
                    $meta_query[] = array(
                        'key' => 'deadline',
                        'value' => $now,
                        'compare' => '>=',
                        'type' => 'DATE'
                    );
                    break;
                case 'expiring_soon':
                    $meta_query[] = array(
                        'relation' => 'AND',
                        array(
                            'key' => 'deadline',
                            'value' => $now,
                            'compare' => '>=',
                            'type' => 'DATE'
                        ),
                        array(
                            'key' => 'deadline',
                            'value' => $soon,
                            'compare' => '<=',
                            'type' => 'DATE'
                        )
                    );
                    break;
                case 'expired':
                    $meta_query[] = array(
                        'key' => 'deadline',
                        'value' => $now,
                        'compare' => '<',
                        'type' => 'DATE'
                    );
                    break;
            }
        }
        
        if (!empty($meta_query)) {
            if (count($meta_query) > 1) {
                $meta_query['relation'] = 'AND';
            }
            $query->set('meta_query', $meta_query);
        }
    }
}
add_action('pre_get_posts', 'inthub_scholarship_admin_filter_query');
