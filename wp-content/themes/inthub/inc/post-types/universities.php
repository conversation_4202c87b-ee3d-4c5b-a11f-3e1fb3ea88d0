<?php
/**
 * Universities Custom Post Type
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Universities Post Type
 */
function inthub_register_universities_post_type() {
    register_post_type('university', array(
        'labels' => array(
            'name' => __('Trường Đại H<PERSON>', 'inthub'),
            'singular_name' => __('Trường Đại Học', 'inthub'),
            'add_new' => __('Thêm trường mới', 'inthub'),
            'add_new_item' => __('Thêm trường đại học mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa trường đại học', 'inthub'),
            'new_item' => __('Trường đại học mới', 'inthub'),
            'view_item' => __('Xem trường đại học', 'inthub'),
            'search_items' => __('Tìm kiếm trường đại học', 'inthub'),
            'not_found' => __('Không tìm thấy trường đại học nào', 'inthub'),
            'not_found_in_trash' => __('Không có trường đại học nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả trường đại học', 'inthub'),
            'archives' => __('Lưu trữ trường đại học', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-building',
        'menu_position' => 6,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'truong-dai-hoc',
            'with_front' => false,
        ),
    ));
}
add_action('init', 'inthub_register_universities_post_type');
