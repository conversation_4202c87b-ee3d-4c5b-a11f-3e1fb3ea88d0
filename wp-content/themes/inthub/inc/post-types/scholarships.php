<?php
/**
 * Scholarships Custom Post Type
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Scholarships Post Type
 */
function inthub_register_scholarships_post_type() {
    register_post_type('scholarship', array(
        'labels' => array(
            'name' => __('Học Bổng', 'inthub'),
            'singular_name' => __('Học Bổng', 'inthub'),
            'add_new' => __('Thêm học bổng mới', 'inthub'),
            'add_new_item' => __('Thêm học bổng mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa học bổng', 'inthub'),
            'new_item' => __('Học bổng mới', 'inthub'),
            'view_item' => __('Xem học bổng', 'inthub'),
            'search_items' => __('Tìm kiếm học bổng', 'inthub'),
            'not_found' => __('Không tìm thấy học bổng nào', 'inthub'),
            'not_found_in_trash' => __('Không có học bổng nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả học bổng', 'inthub'),
            'archives' => __('Lưu trữ học bổng', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-awards',
        'menu_position' => 7,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'hoc-bong',
            'with_front' => false,
        ),
    ));
}
add_action('init', 'inthub_register_scholarships_post_type');
