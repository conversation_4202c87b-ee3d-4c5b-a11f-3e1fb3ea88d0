<?php
/**
 * Events Custom Post Type
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Events Post Type
 */
function inthub_register_events_post_type() {
    register_post_type('event', array(
        'labels' => array(
            'name' => __('Sự kiện', 'inthub'),
            'singular_name' => __('Sự kiện', 'inthub'),
            'add_new' => __('Thêm sự kiện mới', 'inthub'),
            'add_new_item' => __('Thêm sự kiện mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa sự kiện', 'inthub'),
            'new_item' => __('Sự kiện mới', 'inthub'),
            'view_item' => __('Xem sự kiện', 'inthub'),
            'search_items' => __('Tìm kiếm sự kiện', 'inthub'),
            'not_found' => __('Không tìm thấy sự kiện nào', 'inthub'),
            'not_found_in_trash' => __('Không có sự kiện nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả sự kiện', 'inthub'),
            'archives' => __('Lưu trữ sự kiện', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-calendar-alt',
        'menu_position' => 5,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'su-kien',
            'with_front' => false,
        ),
    ));
}
add_action('init', 'inthub_register_events_post_type');

/**
 * Add Event Query Vars for Frontend Filtering
 */
function inthub_event_query_vars($vars) {
    $vars[] = 'event_status';
    $vars[] = 'event_location';
    return $vars;
}
add_filter('query_vars', 'inthub_event_query_vars');

/**
 * Modify Event Archive Query
 */
function inthub_event_archive_query($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('event')) {
        // Default sort by start date
        $query->set('meta_key', 'event_start_datetime');
        $query->set('orderby', 'meta_value');
        $query->set('order', 'ASC');

        // Filter by event status if requested
        $event_status = get_query_var('event_status');
        if ($event_status) {
            $meta_query = array();
            $now = current_time('mysql');

            switch ($event_status) {
                case 'upcoming':
                    $meta_query[] = array(
                        'key' => 'event_start_datetime',
                        'value' => $now,
                        'compare' => '>',
                        'type' => 'DATETIME'
                    );
                    break;
                case 'ongoing':
                    $meta_query[] = array(
                        'relation' => 'AND',
                        array(
                            'key' => 'event_start_datetime',
                            'value' => $now,
                            'compare' => '<=',
                            'type' => 'DATETIME'
                        ),
                        array(
                            'key' => 'event_end_datetime',
                            'value' => $now,
                            'compare' => '>=',
                            'type' => 'DATETIME'
                        )
                    );
                    break;
                case 'past':
                    $meta_query[] = array(
                        'key' => 'event_end_datetime',
                        'value' => $now,
                        'compare' => '<',
                        'type' => 'DATETIME'
                    );
                    break;
            }

            if (!empty($meta_query)) {
                $query->set('meta_query', $meta_query);
            }
        }
    }
}
add_action('pre_get_posts', 'inthub_event_archive_query');

/**
 * Default Sort Events by Start Date
 */
function inthub_event_default_sort($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    if ($query->get('post_type') === 'event' && !$query->get('orderby')) {
        $query->set('meta_key', 'event_start_datetime');
        $query->set('orderby', 'meta_value');
        $query->set('order', 'ASC');
    }
}
add_action('pre_get_posts', 'inthub_event_default_sort');

/**
 * Add Event Structured Data (Schema.org)
 */
function inthub_event_structured_data() {
    if (is_singular('event')) {
        global $post;

        $start_datetime = get_post_meta($post->ID, 'event_start_datetime', true);
        $end_datetime = get_post_meta($post->ID, 'event_end_datetime', true);
        $location = get_post_meta($post->ID, 'event_location', true);
        $short_description = get_post_meta($post->ID, 'event_short_description', true);

        if ($start_datetime && $end_datetime && $location) {
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'Event',
                'name' => get_the_title(),
                'description' => $short_description ?: get_the_excerpt(),
                'startDate' => date('c', strtotime($start_datetime)),
                'endDate' => date('c', strtotime($end_datetime)),
                'location' => array(
                    '@type' => 'Place',
                    'name' => $location,
                    'address' => $location
                ),
                'organizer' => array(
                    '@type' => 'Organization',
                    'name' => get_bloginfo('name'),
                    'url' => home_url()
                )
            );

            if (has_post_thumbnail()) {
                $schema['image'] = get_the_post_thumbnail_url($post->ID, 'large');
            }

            echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }
}
add_action('wp_head', 'inthub_event_structured_data');
