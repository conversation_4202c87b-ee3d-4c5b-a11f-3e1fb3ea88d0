<?php
/**
 * <PERSON><PERSON><PERSON> (Major/Field of Study) Custom Post Type
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Nganh Hoc (Major/Field of Study) Custom Post Type
 */
function inthub_register_nganh_hoc_post_type() {
    register_post_type('nganh_hoc', array(
        'labels' => array(
            'name' => __('Ngành học', 'inthub'),
            'singular_name' => __('Ngành học', 'inthub'),
            'add_new' => __('Thêm ngành học mới', 'inthub'),
            'add_new_item' => __('Thêm ngành học mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa ngành học', 'inthub'),
            'new_item' => __('Ngành học mới', 'inthub'),
            'view_item' => __('<PERSON>em ngành học', 'inthub'),
            'search_items' => __('Tìm kiếm ngành học', 'inthub'),
            'not_found' => __('Không tìm thấy ngành học nào', 'inthub'),
            'not_found_in_trash' => __('Không có ngành học nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả ngành học', 'inthub'),
            'archives' => __('Lưu trữ ngành học', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-welcome-learn-more',
        'menu_position' => 8,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'nganh-hoc',
            'with_front' => false,
        ),
    ));
}
add_action('init', 'inthub_register_nganh_hoc_post_type');
