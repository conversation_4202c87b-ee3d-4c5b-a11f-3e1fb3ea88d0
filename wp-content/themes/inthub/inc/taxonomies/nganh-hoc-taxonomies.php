<?php
/**
 * Nganh Hoc Taxonomies
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Nganh Hoc Taxonomies
 */
function inthub_register_nganh_hoc_taxonomies() {
    // Linh Vuc (Field/Domain) Taxonomy
    register_taxonomy('linh_vuc', 'nganh_hoc', array(
        'labels' => array(
            'name' => __('Lĩnh vực', 'inthub'),
            'singular_name' => __('Lĩnh vực', 'inthub'),
            'menu_name' => __('Lĩnh vực', 'inthub'),
            'all_items' => __('Tất cả lĩnh vực', 'inthub'),
            'edit_item' => __('Chỉnh sửa lĩnh vực', 'inthub'),
            'view_item' => __('Xem lĩnh vực', 'inthub'),
            'update_item' => __('Cậ<PERSON> nhật lĩnh vực', 'inthub'),
            'add_new_item' => __('Thêm lĩnh vực mới', 'inthub'),
            'new_item_name' => __('Tên lĩnh vực mới', 'inthub'),
            'parent_item' => __('Lĩnh vực cha', 'inthub'),
            'parent_item_colon' => __('Lĩnh vực cha:', 'inthub'),
            'search_items' => __('Tìm kiếm lĩnh vực', 'inthub'),
            'popular_items' => __('Lĩnh vực phổ biến', 'inthub'),
            'separate_items_with_commas' => __('Phân cách lĩnh vực bằng dấu phẩy', 'inthub'),
            'add_or_remove_items' => __('Thêm hoặc xóa lĩnh vực', 'inthub'),
            'choose_from_most_used' => __('Chọn từ lĩnh vực thường dùng', 'inthub'),
            'not_found' => __('Không tìm thấy lĩnh vực nào', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'hierarchical' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array(
            'slug' => 'linh-vuc',
            'with_front' => false,
            'hierarchical' => true,
        ),
        'capabilities' => array(
            'manage_terms' => 'manage_categories',
            'edit_terms' => 'manage_categories',
            'delete_terms' => 'manage_categories',
            'assign_terms' => 'edit_posts',
        ),
    ));

    // Cap Do Dao Tao (Education Level) Taxonomy
    register_taxonomy('cap_do_dao_tao', 'nganh_hoc', array(
        'labels' => array(
            'name' => __('Cấp độ đào tạo', 'inthub'),
            'singular_name' => __('Cấp độ đào tạo', 'inthub'),
            'menu_name' => __('Cấp độ đào tạo', 'inthub'),
            'all_items' => __('Tất cả cấp độ', 'inthub'),
            'edit_item' => __('Chỉnh sửa cấp độ', 'inthub'),
            'view_item' => __('Xem cấp độ', 'inthub'),
            'update_item' => __('Cập nhật cấp độ', 'inthub'),
            'add_new_item' => __('Thêm cấp độ mới', 'inthub'),
            'new_item_name' => __('Tên cấp độ mới', 'inthub'),
            'parent_item' => __('Cấp độ cha', 'inthub'),
            'parent_item_colon' => __('Cấp độ cha:', 'inthub'),
            'search_items' => __('Tìm kiếm cấp độ', 'inthub'),
            'popular_items' => __('Cấp độ phổ biến', 'inthub'),
            'separate_items_with_commas' => __('Phân cách cấp độ bằng dấu phẩy', 'inthub'),
            'add_or_remove_items' => __('Thêm hoặc xóa cấp độ', 'inthub'),
            'choose_from_most_used' => __('Chọn từ cấp độ thường dùng', 'inthub'),
            'not_found' => __('Không tìm thấy cấp độ nào', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'hierarchical' => false,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array(
            'slug' => 'cap-do-dao-tao',
            'with_front' => false,
        ),
        'capabilities' => array(
            'manage_terms' => 'manage_categories',
            'edit_terms' => 'manage_categories',
            'delete_terms' => 'manage_categories',
            'assign_terms' => 'edit_posts',
        ),
    ));
}
add_action('init', 'inthub_register_nganh_hoc_taxonomies');

/**
 * Create Default Terms for Nganh Hoc Taxonomies
 */
function inthub_create_default_nganh_hoc_terms() {
    // Check if terms already exist
    if (get_option('inthub_nganh_hoc_terms_created')) {
        return;
    }

    // Default Linh Vuc (Fields) terms
    $linh_vuc_terms = array(
        'cong-nghe-thong-tin' => 'Công nghệ thông tin',
        'kinh-te-quan-tri' => 'Kinh tế - Quản trị',
        'y-te-duoc-pham' => 'Y tế - Dược phẩm',
        'ky-thuat-cong-nghe' => 'Kỹ thuật - Công nghệ',
        'khoa-hoc-tu-nhien' => 'Khoa học tự nhiên',
        'khoa-hoc-xa-hoi' => 'Khoa học xã hội',
        'nghe-thuat-thiet-ke' => 'Nghệ thuật - Thiết kế',
        'giao-duc-su-pham' => 'Giáo dục - Sư phạm',
        'luat-chinh-tri' => 'Luật - Chính trị',
        'nong-lam-ngu-nghiep' => 'Nông - Lâm - Ngư nghiệp',
        'du-lich-khach-san' => 'Du lịch - Khách sạn',
        'truyen-thong-bao-chi' => 'Truyền thông - Báo chí',
        'the-thao-the-duc' => 'Thể thao - Thể dục',
        'moi-truong-tai-nguyen' => 'Môi trường - Tài nguyên'
    );

    foreach ($linh_vuc_terms as $slug => $name) {
        wp_insert_term($name, 'linh_vuc', array('slug' => $slug));
    }

    // Default Cap Do Dao Tao (Education Levels) terms
    $cap_do_terms = array(
        'cao-dang' => 'Cao đẳng',
        'dai-hoc' => 'Đại học',
        'thac-si' => 'Thạc sĩ',
        'tien-si' => 'Tiến sĩ',
        'chung-chi-nghe' => 'Chứng chỉ nghề',
        'lien-thong' => 'Liên thông'
    );

    foreach ($cap_do_terms as $slug => $name) {
        wp_insert_term($name, 'cap_do_dao_tao', array('slug' => $slug));
    }

    // Mark as created
    update_option('inthub_nganh_hoc_terms_created', true);
}
add_action('init', 'inthub_create_default_nganh_hoc_terms', 22);
