<?php
/**
 * Scholarship Categories Taxonomy
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Register Scholarship Category Taxonomy
 */
function inthub_register_scholarship_category_taxonomy() {
    register_taxonomy('scholarship_category', 'scholarship', array(
        'labels' => array(
            'name' => __('<PERSON>h mục học bổng', 'inthub'),
            'singular_name' => __('<PERSON>h mục học bổng', 'inthub'),
            'menu_name' => __('<PERSON>h mục học bổng', 'inthub'),
            'all_items' => __('Tất cả danh mục', 'inthub'),
            'edit_item' => __('Chỉnh sửa danh mục', 'inthub'),
            'view_item' => __('Xem danh mục', 'inthub'),
            'update_item' => __('Cập nhật danh mục', 'inthub'),
            'add_new_item' => __('Thêm danh mục mới', 'inthub'),
            'new_item_name' => __('Tên danh mục mới', 'inthub'),
            'parent_item' => __('Danh mục cha', 'inthub'),
            'parent_item_colon' => __('Danh mục cha:', 'inthub'),
            'search_items' => __('Tìm kiếm danh mục', 'inthub'),
            'popular_items' => __('Danh mục phổ biến', 'inthub'),
            'separate_items_with_commas' => __('Phân cách danh mục bằng dấu phẩy', 'inthub'),
            'add_or_remove_items' => __('Thêm hoặc xóa danh mục', 'inthub'),
            'choose_from_most_used' => __('Chọn từ danh mục thường dùng', 'inthub'),
            'not_found' => __('Không tìm thấy danh mục nào', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'hierarchical' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array(
            'slug' => 'danh-muc-hoc-bong',
            'with_front' => false,
            'hierarchical' => true,
        ),
        'capabilities' => array(
            'manage_terms' => 'manage_categories',
            'edit_terms' => 'manage_categories',
            'delete_terms' => 'manage_categories',
            'assign_terms' => 'edit_posts',
        ),
    ));
}
add_action('init', 'inthub_register_scholarship_category_taxonomy');

/**
 * Create Default Scholarship Categories
 */
function inthub_create_default_scholarship_categories() {
    // Check if categories already exist
    if (get_option('inthub_scholarship_categories_created')) {
        return;
    }

    // Default categories structure
    $categories = array(
        // Theo loại học bổng
        'loai-hoc-bong' => array(
            'name' => 'Theo loại học bổng',
            'description' => 'Phân loại học bổng theo mức độ hỗ trợ và tính chất',
            'children' => array(
                'hoc-bong-toan-phan' => array(
                    'name' => 'Học bổng toàn phần',
                    'description' => 'Học bổng chi trả 100% học phí và sinh hoạt phí'
                ),
                'hoc-bong-ban-phan' => array(
                    'name' => 'Học bổng bán phần',
                    'description' => 'Học bổng chi trả một phần học phí hoặc sinh hoạt phí'
                ),
                'hoc-bong-nghien-cuu' => array(
                    'name' => 'Học bổng nghiên cứu',
                    'description' => 'Học bổng dành cho các dự án nghiên cứu khoa học'
                ),
                'hoc-bong-the-thao' => array(
                    'name' => 'Học bổng thể thao',
                    'description' => 'Học bổng dành cho sinh viên có tài năng thể thao'
                ),
                'hoc-bong-nghe-thuat' => array(
                    'name' => 'Học bổng nghệ thuật',
                    'description' => 'Học bổng dành cho sinh viên có tài năng nghệ thuật'
                )
            )
        ),
        // Theo cấp độ
        'cap-do' => array(
            'name' => 'Theo cấp độ',
            'description' => 'Phân loại học bổng theo bậc học',
            'children' => array(
                'dai-hoc' => array(
                    'name' => 'Đại học',
                    'description' => 'Học bổng cho bậc đại học (Bachelor)'
                ),
                'thac-si' => array(
                    'name' => 'Thạc sĩ',
                    'description' => 'Học bổng cho bậc thạc sĩ (Master)'
                ),
                'tien-si' => array(
                    'name' => 'Tiến sĩ',
                    'description' => 'Học bổng cho bậc tiến sĩ (PhD)'
                ),
                'sau-tien-si' => array(
                    'name' => 'Sau tiến sĩ',
                    'description' => 'Học bổng cho nghiên cứu sau tiến sĩ (Postdoc)'
                )
            )
        )
    );

    // Create parent categories first
    $parent_terms = array();
    foreach ($categories as $slug => $category) {
        $term = wp_insert_term(
            $category['name'],
            'scholarship_category',
            array(
                'slug' => $slug,
                'description' => $category['description']
            )
        );

        if (!is_wp_error($term)) {
            $parent_terms[$slug] = $term['term_id'];
        }
    }

    // Create child categories
    foreach ($categories as $parent_slug => $category) {
        if (isset($category['children']) && isset($parent_terms[$parent_slug])) {
            foreach ($category['children'] as $child_slug => $child_category) {
                wp_insert_term(
                    $child_category['name'],
                    'scholarship_category',
                    array(
                        'slug' => $child_slug,
                        'description' => $child_category['description'],
                        'parent' => $parent_terms[$parent_slug]
                    )
                );
            }
        }
    }

    // Mark as created
    update_option('inthub_scholarship_categories_created', true);
}
add_action('init', 'inthub_create_default_scholarship_categories', 21);
