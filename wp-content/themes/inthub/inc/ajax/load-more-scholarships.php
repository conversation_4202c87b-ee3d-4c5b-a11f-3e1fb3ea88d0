<?php
/**
 * AJAX Load More Scholarships
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX Load More Scholarships
 */
function inthub_load_more_scholarships() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'inthub_nonce')) {
        wp_die(__('Security check failed', 'inthub'));
    }

    // Get parameters
    $page = intval($_POST['page']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 6;
    $category = sanitize_text_field($_POST['category']);
    $country = sanitize_text_field($_POST['country']);
    $level = sanitize_text_field($_POST['level']);
    $search = sanitize_text_field($_POST['search']);

    // Build query arguments
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC'
    );

    $tax_query = array();
    $meta_query = array();

    // Add taxonomy query if category is specified
    if (!empty($category)) {
        $tax_query[] = array(
            'taxonomy' => 'scholarship_category',
            'field' => 'slug',
            'terms' => $category
        );
    }

    // Add meta queries
    if (!empty($country)) {
        $meta_query[] = array(
            'key' => 'country',
            'value' => $country,
            'compare' => '='
        );
    }

    if (!empty($level)) {
        $meta_query[] = array(
            'key' => 'level',
            'value' => $level,
            'compare' => '='
        );
    }

    // Add queries to args
    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }
    if (!empty($meta_query)) {
        if (count($meta_query) > 1) {
            $meta_query['relation'] = 'AND';
        }
        $args['meta_query'] = $meta_query;
    }

    // Add search query if search term is specified
    if (!empty($search)) {
        $args['s'] = $search;
    }

    // Execute query
    $scholarships = new WP_Query($args);

    if ($scholarships->have_posts()) {
        ob_start();
        
        while ($scholarships->have_posts()) {
            $scholarships->the_post();
            
            // Get scholarship meta data
            $scholarship_name = get_post_meta(get_the_ID(), 'scholarship_name', true);
            $provider = get_post_meta(get_the_ID(), 'provider', true);
            $scholarship_value = get_post_meta(get_the_ID(), 'scholarship_value', true);
            $deadline = get_post_meta(get_the_ID(), 'deadline', true);
            $country = get_post_meta(get_the_ID(), 'country', true);
            $level = get_post_meta(get_the_ID(), 'level', true);
            $field_of_study = get_post_meta(get_the_ID(), 'field_of_study', true);
            $website_url = get_post_meta(get_the_ID(), 'website_url', true);
            
            // Get scholarship categories
            $categories = get_the_terms(get_the_ID(), 'scholarship_category');
            
            // Country labels
            $country_labels = array(
                'usa' => 'Hoa Kỳ',
                'uk' => 'Anh',
                'canada' => 'Canada',
                'australia' => 'Úc',
                'germany' => 'Đức',
                'france' => 'Pháp',
                'japan' => 'Nhật Bản',
                'south_korea' => 'Hàn Quốc',
                'singapore' => 'Singapore',
                'netherlands' => 'Hà Lan',
                'other' => 'Khác'
            );
            
            // Level labels
            $level_labels = array(
                'undergraduate' => 'Đại học',
                'master' => 'Thạc sĩ',
                'phd' => 'Tiến sĩ',
                'postdoc' => 'Sau tiến sĩ',
                'all' => 'Tất cả bậc học'
            );
            
            // Calculate deadline status
            $deadline_status = '';
            $deadline_class = '';
            if ($deadline) {
                $now = current_time('timestamp');
                $deadline_timestamp = strtotime($deadline);
                $days_left = floor(($deadline_timestamp - $now) / (60 * 60 * 24));
                
                if ($days_left < 0) {
                    $deadline_status = 'Hết hạn';
                    $deadline_class = 'text-red-600';
                } elseif ($days_left <= 7) {
                    $deadline_status = $days_left . ' ngày';
                    $deadline_class = 'text-red-600 font-bold';
                } elseif ($days_left <= 30) {
                    $deadline_status = $days_left . ' ngày';
                    $deadline_class = 'text-yellow-600 font-bold';
                } else {
                    $deadline_status = date('d/m/Y', $deadline_timestamp);
                    $deadline_class = 'text-green-600';
                }
            }
            ?>
            
            <div class="scholarship-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <?php if (has_post_thumbnail()) : ?>
                    <div class="scholarship-image">
                        <a href="<?php the_permalink(); ?>">
                            <?php the_post_thumbnail('medium', array('class' => 'w-full h-48 object-cover')); ?>
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="scholarship-content p-6">
                    <h3 class="scholarship-title text-xl font-semibold mb-2">
                        <a href="<?php the_permalink(); ?>" class="text-blue-900 hover:text-pink-500 transition-colors duration-200">
                            <?php echo $scholarship_name ?: get_the_title(); ?>
                        </a>
                    </h3>
                    
                    <?php if ($provider) : ?>
                        <p class="scholarship-provider text-gray-600 mb-2">
                            <i class="fas fa-university mr-1"></i>
                            <?php echo esc_html($provider); ?>
                        </p>
                    <?php endif; ?>
                    
                    <?php if ($scholarship_value) : ?>
                        <p class="scholarship-value text-green-600 font-semibold mb-2">
                            <i class="fas fa-dollar-sign mr-1"></i>
                            <?php echo esc_html($scholarship_value); ?>
                        </p>
                    <?php endif; ?>
                    
                    <div class="scholarship-meta grid grid-cols-2 gap-2 mb-3 text-sm text-gray-600">
                        <?php if ($country && isset($country_labels[$country])) : ?>
                            <div>
                                <i class="fas fa-flag mr-1"></i>
                                <?php echo esc_html($country_labels[$country]); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($level && isset($level_labels[$level])) : ?>
                            <div>
                                <i class="fas fa-graduation-cap mr-1"></i>
                                <?php echo esc_html($level_labels[$level]); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($field_of_study) : ?>
                            <div class="col-span-2">
                                <i class="fas fa-book mr-1"></i>
                                <?php echo esc_html($field_of_study); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <?php if ($deadline) : ?>
                        <div class="scholarship-deadline mb-3">
                            <span class="text-gray-700">Hạn nộp: </span>
                            <span class="<?php echo $deadline_class; ?>">
                                <i class="fas fa-clock mr-1"></i>
                                <?php echo $deadline_status; ?>
                            </span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($categories && !is_wp_error($categories)) : ?>
                        <div class="scholarship-categories mb-3">
                            <?php foreach ($categories as $category) : ?>
                                <span class="inline-block bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                    <?php echo esc_html($category->name); ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="scholarship-excerpt text-gray-700 mb-4">
                        <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                    </div>
                    
                    <div class="scholarship-actions flex justify-between items-center">
                        <a href="<?php the_permalink(); ?>" class="btn-primary bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition-colors duration-200">
                            <?php _e('Xem chi tiết', 'inthub'); ?>
                        </a>
                        
                        <?php if ($website_url) : ?>
                            <a href="<?php echo esc_url($website_url); ?>" target="_blank" rel="noopener" class="btn-secondary text-purple-600 hover:text-purple-800 transition-colors duration-200">
                                <i class="fas fa-external-link-alt mr-1"></i>
                                <?php _e('Đăng ký', 'inthub'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <?php
        }
        
        wp_reset_postdata();
        
        $content = ob_get_clean();
        
        // Prepare response
        $response = array(
            'success' => true,
            'content' => $content,
            'has_more' => $page < $scholarships->max_num_pages,
            'current_page' => $page,
            'max_pages' => $scholarships->max_num_pages,
            'total_posts' => $scholarships->found_posts
        );
        
    } else {
        $response = array(
            'success' => false,
            'message' => __('Không tìm thấy học bổng nào.', 'inthub'),
            'has_more' => false
        );
    }

    wp_send_json($response);
}
add_action('wp_ajax_load_more_scholarships', 'inthub_load_more_scholarships');
add_action('wp_ajax_nopriv_load_more_scholarships', 'inthub_load_more_scholarships');

/**
 * AJAX Filter Scholarships
 */
function inthub_filter_scholarships() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'inthub_nonce')) {
        wp_die(__('Security check failed', 'inthub'));
    }

    // Get filter parameters
    $category = sanitize_text_field($_POST['category']);
    $country = sanitize_text_field($_POST['country']);
    $level = sanitize_text_field($_POST['level']);
    $search = sanitize_text_field($_POST['search']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 6;

    // Build query arguments
    $args = array(
        'post_type' => 'scholarship',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => 1,
        'orderby' => 'date',
        'order' => 'DESC'
    );

    $tax_query = array();
    $meta_query = array();

    // Add taxonomy query if category is specified
    if (!empty($category) && $category !== 'all') {
        $tax_query[] = array(
            'taxonomy' => 'scholarship_category',
            'field' => 'slug',
            'terms' => $category
        );
    }

    // Add meta queries
    if (!empty($country) && $country !== 'all') {
        $meta_query[] = array(
            'key' => 'country',
            'value' => $country,
            'compare' => '='
        );
    }

    if (!empty($level) && $level !== 'all') {
        $meta_query[] = array(
            'key' => 'level',
            'value' => $level,
            'compare' => '='
        );
    }

    // Add queries to args
    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }
    if (!empty($meta_query)) {
        if (count($meta_query) > 1) {
            $meta_query['relation'] = 'AND';
        }
        $args['meta_query'] = $meta_query;
    }

    // Add search query if search term is specified
    if (!empty($search)) {
        $args['s'] = $search;
    }

    // Execute query
    $scholarships = new WP_Query($args);

    if ($scholarships->have_posts()) {
        ob_start();
        
        while ($scholarships->have_posts()) {
            $scholarships->the_post();
            // Use the same template as load more function
            include(locate_template('template-parts/scholarship-card.php'));
        }
        
        wp_reset_postdata();
        
        $content = ob_get_clean();
        
        $response = array(
            'success' => true,
            'content' => $content,
            'has_more' => 1 < $scholarships->max_num_pages,
            'current_page' => 1,
            'max_pages' => $scholarships->max_num_pages,
            'total_posts' => $scholarships->found_posts
        );
        
    } else {
        $response = array(
            'success' => false,
            'message' => __('Không tìm thấy học bổng nào phù hợp với bộ lọc.', 'inthub'),
            'has_more' => false,
            'total_posts' => 0
        );
    }

    wp_send_json($response);
}
add_action('wp_ajax_filter_scholarships', 'inthub_filter_scholarships');
add_action('wp_ajax_nopriv_filter_scholarships', 'inthub_filter_scholarships');
