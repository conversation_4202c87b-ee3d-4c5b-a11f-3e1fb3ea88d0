<?php
/**
 * AJAX Load More Universities
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * AJAX Load More Universities
 */
function inthub_load_more_universities() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'inthub_nonce')) {
        wp_die(__('Security check failed', 'inthub'));
    }

    // Get parameters
    $page = intval($_POST['page']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 6;
    $category = sanitize_text_field($_POST['category']);
    $country = sanitize_text_field($_POST['country']);
    $search = sanitize_text_field($_POST['search']);

    // Build query arguments
    $args = array(
        'post_type' => 'university',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => $page,
        'orderby' => 'date',
        'order' => 'DESC'
    );

    // Add taxonomy query if category is specified
    if (!empty($category)) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'university_category',
                'field' => 'slug',
                'terms' => $category
            )
        );
    }

    // Add meta query if country is specified
    if (!empty($country)) {
        $args['meta_query'] = array(
            array(
                'key' => 'country',
                'value' => $country,
                'compare' => '='
            )
        );
    }

    // Add search query if search term is specified
    if (!empty($search)) {
        $args['s'] = $search;
    }

    // Execute query
    $universities = new WP_Query($args);

    if ($universities->have_posts()) {
        ob_start();
        
        while ($universities->have_posts()) {
            $universities->the_post();
            
            // Get university meta data
            $university_name = get_post_meta(get_the_ID(), 'university_name', true);
            $location = get_post_meta(get_the_ID(), 'location', true);
            $country = get_post_meta(get_the_ID(), 'country', true);
            $website_url = get_post_meta(get_the_ID(), 'website_url', true);
            $tuition_fee = get_post_meta(get_the_ID(), 'tuition_fee', true);
            
            // Get university categories
            $categories = get_the_terms(get_the_ID(), 'university_category');
            
            // Country labels
            $country_labels = array(
                'usa' => 'Hoa Kỳ',
                'uk' => 'Anh',
                'canada' => 'Canada',
                'australia' => 'Úc',
                'germany' => 'Đức',
                'france' => 'Pháp',
                'japan' => 'Nhật Bản',
                'south_korea' => 'Hàn Quốc',
                'singapore' => 'Singapore',
                'netherlands' => 'Hà Lan',
                'other' => 'Khác'
            );
            ?>
            
            <div class="university-card bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <?php if (has_post_thumbnail()) : ?>
                    <div class="university-image">
                        <a href="<?php the_permalink(); ?>">
                            <?php the_post_thumbnail('medium', array('class' => 'w-full h-48 object-cover')); ?>
                        </a>
                    </div>
                <?php endif; ?>
                
                <div class="university-content p-6">
                    <h3 class="university-title text-xl font-semibold mb-2">
                        <a href="<?php the_permalink(); ?>" class="text-blue-900 hover:text-pink-500 transition-colors duration-200">
                            <?php echo $university_name ?: get_the_title(); ?>
                        </a>
                    </h3>
                    
                    <?php if ($location) : ?>
                        <p class="university-location text-gray-600 mb-2">
                            <i class="fas fa-map-marker-alt mr-1"></i>
                            <?php echo esc_html($location); ?>
                        </p>
                    <?php endif; ?>
                    
                    <?php if ($country && isset($country_labels[$country])) : ?>
                        <p class="university-country text-gray-600 mb-2">
                            <i class="fas fa-flag mr-1"></i>
                            <?php echo esc_html($country_labels[$country]); ?>
                        </p>
                    <?php endif; ?>
                    
                    <?php if ($tuition_fee) : ?>
                        <p class="university-tuition text-gray-600 mb-3">
                            <i class="fas fa-dollar-sign mr-1"></i>
                            <?php echo esc_html($tuition_fee); ?>
                        </p>
                    <?php endif; ?>
                    
                    <?php if ($categories && !is_wp_error($categories)) : ?>
                        <div class="university-categories mb-3">
                            <?php foreach ($categories as $category) : ?>
                                <span class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">
                                    <?php echo esc_html($category->name); ?>
                                </span>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <div class="university-excerpt text-gray-700 mb-4">
                        <?php echo wp_trim_words(get_the_excerpt(), 20, '...'); ?>
                    </div>
                    
                    <div class="university-actions flex justify-between items-center">
                        <a href="<?php the_permalink(); ?>" class="btn-primary bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors duration-200">
                            <?php _e('Xem chi tiết', 'inthub'); ?>
                        </a>
                        
                        <?php if ($website_url) : ?>
                            <a href="<?php echo esc_url($website_url); ?>" target="_blank" rel="noopener" class="btn-secondary text-blue-600 hover:text-blue-800 transition-colors duration-200">
                                <i class="fas fa-external-link-alt mr-1"></i>
                                <?php _e('Website', 'inthub'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <?php
        }
        
        wp_reset_postdata();
        
        $content = ob_get_clean();
        
        // Prepare response
        $response = array(
            'success' => true,
            'content' => $content,
            'has_more' => $page < $universities->max_num_pages,
            'current_page' => $page,
            'max_pages' => $universities->max_num_pages,
            'total_posts' => $universities->found_posts
        );
        
    } else {
        $response = array(
            'success' => false,
            'message' => __('Không tìm thấy trường đại học nào.', 'inthub'),
            'has_more' => false
        );
    }

    wp_send_json($response);
}
add_action('wp_ajax_load_more_universities', 'inthub_load_more_universities');
add_action('wp_ajax_nopriv_load_more_universities', 'inthub_load_more_universities');

/**
 * AJAX Filter Universities
 */
function inthub_filter_universities() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'inthub_nonce')) {
        wp_die(__('Security check failed', 'inthub'));
    }

    // Get filter parameters
    $category = sanitize_text_field($_POST['category']);
    $country = sanitize_text_field($_POST['country']);
    $search = sanitize_text_field($_POST['search']);
    $posts_per_page = intval($_POST['posts_per_page']) ?: 6;

    // Build query arguments
    $args = array(
        'post_type' => 'university',
        'post_status' => 'publish',
        'posts_per_page' => $posts_per_page,
        'paged' => 1,
        'orderby' => 'date',
        'order' => 'DESC'
    );

    $tax_query = array();
    $meta_query = array();

    // Add taxonomy query if category is specified
    if (!empty($category) && $category !== 'all') {
        $tax_query[] = array(
            'taxonomy' => 'university_category',
            'field' => 'slug',
            'terms' => $category
        );
    }

    // Add meta query if country is specified
    if (!empty($country) && $country !== 'all') {
        $meta_query[] = array(
            'key' => 'country',
            'value' => $country,
            'compare' => '='
        );
    }

    // Add queries to args
    if (!empty($tax_query)) {
        $args['tax_query'] = $tax_query;
    }
    if (!empty($meta_query)) {
        $args['meta_query'] = $meta_query;
    }

    // Add search query if search term is specified
    if (!empty($search)) {
        $args['s'] = $search;
    }

    // Execute query
    $universities = new WP_Query($args);

    if ($universities->have_posts()) {
        ob_start();
        
        while ($universities->have_posts()) {
            $universities->the_post();
            // Use the same template as load more function
            include(locate_template('template-parts/university-card.php'));
        }
        
        wp_reset_postdata();
        
        $content = ob_get_clean();
        
        $response = array(
            'success' => true,
            'content' => $content,
            'has_more' => 1 < $universities->max_num_pages,
            'current_page' => 1,
            'max_pages' => $universities->max_num_pages,
            'total_posts' => $universities->found_posts
        );
        
    } else {
        $response = array(
            'success' => false,
            'message' => __('Không tìm thấy trường đại học nào phù hợp với bộ lọc.', 'inthub'),
            'has_more' => false,
            'total_posts' => 0
        );
    }

    wp_send_json($response);
}
add_action('wp_ajax_filter_universities', 'inthub_filter_universities');
add_action('wp_ajax_nopriv_filter_universities', 'inthub_filter_universities');
