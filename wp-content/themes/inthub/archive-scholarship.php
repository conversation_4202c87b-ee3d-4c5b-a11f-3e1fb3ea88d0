<?php
/**
 * Archive template for Scholarship post type
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header(); ?>

<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-[#fa3c80] to-[#004aad] py-16 bg-gradient">
        <div class="container mx-auto px-4">
            <div class="text-center text-white">
                <h1 class="text-4xl md:text-5xl font-bold mb-4">
                    <?php _e('Học Bổng Du Học', 'inthub'); ?>
                </h1>
                <p class="text-xl opacity-90 max-w-2xl mx-auto">
                    <?php _e('Khám phá các cơ hội học bổng tuyệt vời từ các trường đại học hàng đầu thế giới', 'inthub'); ?>
                </p>
            </div>
        </div>
    </section>

    <!-- Filter Section -->
    <section class="py-8 bg-white border-b">
        <div class="container mx-auto px-4">
            <div class="flex flex-wrap gap-4 items-center justify-between">
                <div class="flex flex-wrap gap-4">
                    <div class="filter-group">
                        <label for="category-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Danh mục:', 'inthub'); ?>
                        </label>
                        <select id="category-filter" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value=""><?php _e('Tất cả danh mục', 'inthub'); ?></option>
                            <?php
                            $categories = get_terms(array(
                                'taxonomy' => 'scholarship_category',
                                'hide_empty' => false,
                                'parent' => 0, // Only get parent categories
                            ));
                            if ($categories && !is_wp_error($categories)) :
                                foreach ($categories as $category) :
                                    $selected = (isset($_GET['category']) && $_GET['category'] == $category->slug) ? 'selected' : '';
                                    ?>
                                    <option value="<?php echo esc_attr($category->slug); ?>" <?php echo $selected; ?>>
                                        <?php echo esc_html($category->name); ?>
                                    </option>
                                    <?php
                                    // Get child categories
                                    $child_categories = get_terms(array(
                                        'taxonomy' => 'scholarship_category',
                                        'hide_empty' => false,
                                        'parent' => $category->term_id,
                                    ));
                                    if ($child_categories && !is_wp_error($child_categories)) :
                                        foreach ($child_categories as $child_category) :
                                            $child_selected = (isset($_GET['category']) && $_GET['category'] == $child_category->slug) ? 'selected' : '';
                                            ?>
                                            <option value="<?php echo esc_attr($child_category->slug); ?>" <?php echo $child_selected; ?>>
                                                &nbsp;&nbsp;└ <?php echo esc_html($child_category->name); ?>
                                            </option>
                                            <?php
                                        endforeach;
                                    endif;
                                endforeach;
                            endif;
                            ?>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="university-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Trường đại học:', 'inthub'); ?>
                        </label>
                        <select id="university-filter" class="border border-gray-300 rounded-md px-3 py-2 min-w-48">
                            <option value=""><?php _e('Tất cả trường', 'inthub'); ?></option>
                            <?php
                            $universities = get_posts(array(
                                'post_type' => 'university',
                                'posts_per_page' => -1,
                                'post_status' => 'publish',
                                'orderby' => 'title',
                                'order' => 'ASC'
                            ));
                            foreach ($universities as $university) :
                                $university_name = get_post_meta($university->ID, 'university_name', true);
                                $selected = (isset($_GET['university']) && $_GET['university'] == $university->ID) ? 'selected' : '';
                            ?>
                                <option value="<?php echo $university->ID; ?>" <?php echo $selected; ?>>
                                    <?php echo esc_html($university_name ?: $university->post_title); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="country-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Quốc gia:', 'inthub'); ?>
                        </label>
                        <select id="country-filter" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value=""><?php _e('Tất cả quốc gia', 'inthub'); ?></option>
                            <option value="usa"><?php _e('Hoa Kỳ', 'inthub'); ?></option>
                            <option value="uk"><?php _e('Anh', 'inthub'); ?></option>
                            <option value="canada"><?php _e('Canada', 'inthub'); ?></option>
                            <option value="australia"><?php _e('Úc', 'inthub'); ?></option>
                            <option value="germany"><?php _e('Đức', 'inthub'); ?></option>
                            <option value="france"><?php _e('Pháp', 'inthub'); ?></option>
                            <option value="japan"><?php _e('Nhật Bản', 'inthub'); ?></option>
                            <option value="south_korea"><?php _e('Hàn Quốc', 'inthub'); ?></option>
                            <option value="singapore"><?php _e('Singapore', 'inthub'); ?></option>
                            <option value="netherlands"><?php _e('Hà Lan', 'inthub'); ?></option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="value-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Giá trị:', 'inthub'); ?>
                        </label>
                        <select id="value-filter" class="border border-gray-300 rounded-md px-3 py-2">
                            <option value=""><?php _e('Tất cả giá trị', 'inthub'); ?></option>
                            <option value="full"><?php _e('Toàn phần', 'inthub'); ?></option>
                            <option value="partial"><?php _e('Một phần', 'inthub'); ?></option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="search-filter" class="block text-sm font-medium text-gray-700 mb-1">
                            <?php _e('Tìm kiếm:', 'inthub'); ?>
                        </label>
                        <input type="text" id="search-filter" placeholder="<?php _e('Tên học bổng...', 'inthub'); ?>" 
                               class="border border-gray-300 rounded-md px-3 py-2 w-64">
                    </div>
                </div>
                <div class="text-sm text-gray-600">
                    <span id="results-count"><?php echo wp_count_posts('scholarship')->publish; ?></span> 
                    <?php _e('học bổng', 'inthub'); ?>
                </div>
            </div>
        </div>
    </section>

    <!-- Scholarships Grid -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div id="scholarships-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php if (have_posts()) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <?php
                        $scholarship_name = get_post_meta(get_the_ID(), 'scholarship_name', true);
                        $scholarship_value = get_post_meta(get_the_ID(), 'scholarship_value', true);
                        $tuition_support = get_post_meta(get_the_ID(), 'tuition_support', true);
                        $related_university_id = get_post_meta(get_the_ID(), 'related_university', true);
                        
                        // Get university info
                        $university = null;
                        $university_name = '';
                        $university_country = '';
                        if ($related_university_id) {
                            $university = get_post($related_university_id);
                            if ($university) {
                                $university_name = get_post_meta($related_university_id, 'university_name', true) ?: $university->post_title;
                                $university_country = get_post_meta($related_university_id, 'country', true);
                            }
                        }
                        
                        // Get country name
                        $country_names = array(
                            'usa' => 'Hoa Kỳ',
                            'uk' => 'Anh',
                            'canada' => 'Canada',
                            'australia' => 'Úc',
                            'germany' => 'Đức',
                            'france' => 'Pháp',
                            'japan' => 'Nhật Bản',
                            'south_korea' => 'Hàn Quốc',
                            'singapore' => 'Singapore',
                            'netherlands' => 'Hà Lan',
                            'other' => 'Khác'
                        );
                        $country_display = isset($country_names[$university_country]) ? $country_names[$university_country] : $university_country;

                        // Get scholarship categories
                        $scholarship_categories = get_the_terms(get_the_ID(), 'scholarship_category');
                        $category_slugs = array();
                        if ($scholarship_categories && !is_wp_error($scholarship_categories)) {
                            foreach ($scholarship_categories as $cat) {
                                $category_slugs[] = $cat->slug;
                            }
                        }

                        // Determine scholarship type for filtering
                        $scholarship_type = '';
                        if ($scholarship_value) {
                            $value_lower = strtolower($scholarship_value);
                            if (strpos($value_lower, '100%') !== false || strpos($value_lower, 'toàn') !== false || strpos($value_lower, 'full') !== false) {
                                $scholarship_type = 'full';
                            } else {
                                $scholarship_type = 'partial';
                            }
                        }
                        ?>
                        
                        <div class="scholarship-card bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
                             data-university="<?php echo esc_attr($related_university_id); ?>"
                             data-country="<?php echo esc_attr($university_country); ?>"
                             data-value="<?php echo esc_attr($scholarship_type); ?>"
                             data-categories="<?php echo esc_attr(implode(',', $category_slugs)); ?>"
                             data-name="<?php echo esc_attr(strtolower($scholarship_name ?: get_the_title())); ?>">

                            <div class="p-6">
                                <!-- Scholarship Categories -->
                                <?php if ($scholarship_categories && !is_wp_error($scholarship_categories)) : ?>
                                    <div class="flex flex-wrap gap-1 mb-3">
                                        <?php foreach ($scholarship_categories as $category) : ?>
                                            <span class="bg-[#fa3c80]/10 text-[#fa3c80] px-2 py-1 rounded text-xs font-medium">
                                                <?php echo esc_html($category->name); ?>
                                            </span>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="flex items-start justify-between mb-4">
                                    <div class="w-12 h-12 bg-[#fa3c80] bg-opacity-10 rounded-full flex items-center justify-center">
                                        <svg class="w-6 h-6 text-[#fa3c80]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                    </div>
                                    <?php if ($country_display) : ?>
                                        <span class="bg-[#004aad] text-white px-3 py-1 rounded-full text-xs font-medium">
                                            <?php echo esc_html($country_display); ?>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                
                                <h3 class="text-xl font-bold text-[#004aad] mb-2">
                                    <a href="<?php the_permalink(); ?>" class="hover:text-[#fa3c80] transition-colors">
                                        <?php echo esc_html($scholarship_name ?: get_the_title()); ?>
                                    </a>
                                </h3>
                                
                                <?php if ($university_name) : ?>
                                    <div class="flex items-center text-gray-600 mb-3">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                        </svg>
                                        <a href="<?php echo get_permalink($related_university_id); ?>" 
                                           class="text-sm hover:text-[#004aad] transition-colors">
                                            <?php echo esc_html($university_name); ?>
                                        </a>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($scholarship_value) : ?>
                                    <div class="flex items-center text-[#fa3c80] font-semibold mb-3">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <span class="text-sm"><?php echo esc_html($scholarship_value); ?></span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if ($tuition_support) : ?>
                                    <div class="bg-gray-50 rounded-lg p-3 mb-4">
                                        <div class="text-xs text-gray-500 mb-1"><?php _e('Hỗ trợ học phí:', 'inthub'); ?></div>
                                        <div class="text-sm text-gray-700"><?php echo esc_html($tuition_support); ?></div>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if (get_the_excerpt()) : ?>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                        <?php echo wp_trim_words(get_the_excerpt(), 20); ?>
                                    </p>
                                <?php endif; ?>
                                
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center text-gray-500 text-xs">
                                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                        </svg>
                                        <span><?php echo get_the_date(); ?></span>
                                    </div>
                                    <a href="<?php the_permalink(); ?>" 
                                       class="inline-flex items-center text-[#004aad] hover:text-[#fa3c80] font-medium text-sm transition-colors">
                                        <?php _e('Xem chi tiết', 'inthub'); ?>
                                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else : ?>
                    <div class="col-span-full text-center py-12">
                        <div class="text-gray-500">
                            <svg class="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="text-xl font-medium mb-2"><?php _e('Không tìm thấy học bổng nào', 'inthub'); ?></h3>
                            <p><?php _e('Hiện tại chưa có học bổng nào được thêm vào hệ thống.', 'inthub'); ?></p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Pagination -->
            <?php if (function_exists('wp_pagenavi')) : ?>
                <div class="mt-12">
                    <?php wp_pagenavi(); ?>
                </div>
            <?php else : ?>
                <div class="mt-12 flex justify-center">
                    <?php
                    the_posts_pagination(array(
                        'mid_size' => 2,
                        'prev_text' => __('&laquo; Trước', 'inthub'),
                        'next_text' => __('Sau &raquo;', 'inthub'),
                        'class' => 'pagination'
                    ));
                    ?>
                </div>
            <?php endif; ?>
        </div>
    </section>
</div>

<?php get_footer(); ?>
