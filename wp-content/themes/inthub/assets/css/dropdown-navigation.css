/**
 * Multi-Level Dropdown Navigation Styles for IntHub Theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

/* ==========================================================================
   Base Navigation Styles
   ========================================================================== */

.main-navigation {
    position: relative;
    z-index: 1000;
}

.main-navigation ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.main-navigation li {
    position: relative;
}

/* ==========================================================================
   Dropdown Menu Base Styles
   ========================================================================== */

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 220px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    padding: 0.5rem 0;
}

/* Show dropdowns on hover/active for all levels */
.dropdown:hover > .dropdown-menu,
.dropdown.active > .dropdown-menu,
.dropdown-submenu:hover > .dropdown-menu,
.dropdown-submenu.active > .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* ==========================================================================
   Multi-Level Dropdown Positioning
   ========================================================================== */

/* First level dropdowns (main menu items) */
.dropdown > .dropdown-menu {
    top: 100%;
    left: 0;
    transform: translateY(-10px);
}

.dropdown:hover > .dropdown-menu,
.dropdown.active > .dropdown-menu {
    transform: translateY(0);
}

/* Second level dropdowns (submenus) */
.dropdown-submenu > .dropdown-menu,
.dropdown-submenu > .sub-menu {
    position: absolute;
    top: 0;
    left: 100%;
    margin-left: 0.5rem;
    transform: translateX(-10px);
    min-width: 200px;
}

.dropdown-submenu:hover > .dropdown-menu,
.dropdown-submenu:hover > .sub-menu,
.dropdown-submenu.active > .dropdown-menu,
.dropdown-submenu.active > .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* Third level and deeper dropdowns */
.dropdown-submenu-level-2 > .dropdown-menu,
.dropdown-submenu-level-2 > .sub-menu,
.dropdown-deep-level > .dropdown-menu,
.dropdown-deep-level > .sub-menu {
    top: 0;
    left: 100%;
    margin-left: 0.5rem;
    transform: translateX(-10px);
}

.dropdown-submenu-level-2:hover > .dropdown-menu,
.dropdown-submenu-level-2:hover > .sub-menu,
.dropdown-deep-level:hover > .dropdown-menu,
.dropdown-deep-level:hover > .sub-menu,
.dropdown-submenu-level-2.active > .dropdown-menu,
.dropdown-submenu-level-2.active > .sub-menu,
.dropdown-deep-level.active > .dropdown-menu,
.dropdown-deep-level.active > .sub-menu {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

/* Right-aligned dropdowns for items near screen edge */
.dropdown.dropdown-right > .dropdown-menu {
    left: auto;
    right: 0;
}

.dropdown-submenu.dropdown-left > .dropdown-menu,
.dropdown-submenu.dropdown-left > .sub-menu {
    left: auto;
    right: 100%;
    margin-left: 0;
    margin-right: 0.5rem;
    transform: translateX(10px);
}

.dropdown-submenu.dropdown-left:hover > .dropdown-menu,
.dropdown-submenu.dropdown-left:hover > .sub-menu,
.dropdown-submenu.dropdown-left.active > .dropdown-menu,
.dropdown-submenu.dropdown-left.active > .sub-menu {
    transform: translateX(0);
}

/* ==========================================================================
   Dropdown Items Styling
   ========================================================================== */

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1.25rem;
    color: #374151;
    text-decoration: none;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border: none;
    background: transparent;
    transition: all 0.2s ease;
}

.dropdown-item:hover,
.dropdown-item:focus {
    color: #ec4899;
    background-color: #f9fafb;
    text-decoration: none;
}

.dropdown-item.active {
    color: #ec4899;
    background-color: #fdf2f8;
}

/* ==========================================================================
   Dropdown Arrows
   ========================================================================== */

.dropdown-arrow,
.dropdown-arrow-sub {
    display: inline-flex;
    align-items: center;
    transition: transform 0.2s ease;
}

.dropdown:hover .dropdown-arrow svg {
    transform: rotate(180deg);
}

.dropdown-submenu:hover .dropdown-arrow-sub svg {
    transform: rotate(-90deg);
}

/* ==========================================================================
   Mobile Responsive Styles
   ========================================================================== */

@media (max-width: 768px) {
    /* Mobile accordion-style dropdowns for all levels */
    .mobile-navigation .dropdown-menu,
    .mobile-navigation .sub-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        border-radius: 0;
        background: #f8fafc;
        margin-top: 0.5rem;
        padding: 0;
        display: none;
        overflow: hidden;
    }

    /* First level mobile dropdowns */
    .mobile-navigation .dropdown.active > .dropdown-menu,
    .mobile-navigation .dropdown.active > .sub-menu {
        display: block;
        margin-left: 1rem;
    }

    /* Second level mobile dropdowns */
    .mobile-navigation .dropdown-submenu.active > .dropdown-menu,
    .mobile-navigation .dropdown-submenu.active > .sub-menu {
        display: block;
        margin-left: 2rem;
        background: #f1f5f9;
    }

    /* Third level and deeper mobile dropdowns */
    .mobile-navigation .dropdown-submenu-level-2.active > .dropdown-menu,
    .mobile-navigation .dropdown-submenu-level-2.active > .sub-menu,
    .mobile-navigation .dropdown-deep-level.active > .dropdown-menu,
    .mobile-navigation .dropdown-deep-level.active > .sub-menu {
        display: block;
        margin-left: 3rem;
        background: #e2e8f0;
    }

    /* Mobile dropdown items styling */
    .mobile-navigation .dropdown-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e5e7eb;
        display: block;
        width: 100%;
    }

    .mobile-navigation .dropdown-item:last-child {
        border-bottom: none;
    }

    /* Mobile dropdown arrows for all levels */
    .mobile-navigation .dropdown-arrow svg,
    .mobile-navigation .dropdown-arrow-sub svg {
        transform: rotate(0deg);
        transition: transform 0.2s ease;
    }

    .mobile-navigation .dropdown.active .dropdown-arrow svg,
    .mobile-navigation .dropdown-submenu.active .dropdown-arrow-sub svg {
        transform: rotate(180deg);
    }

    /* Ensure proper spacing for nested mobile items */
    .mobile-navigation .menu-item-depth-1 {
        margin-left: 1rem;
    }

    .mobile-navigation .menu-item-depth-2 {
        margin-left: 2rem;
    }

    .mobile-navigation .menu-item-depth-3 {
        margin-left: 3rem;
    }
}

/* ==========================================================================
   Accessibility Enhancements
   ========================================================================== */

/* Focus styles for keyboard navigation */
.nav-link:focus,
.dropdown-item:focus {
    outline: 2px solid #ec4899;
    outline-offset: 2px;
}

/* Screen reader only text */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ==========================================================================
   Animation Enhancements
   ========================================================================== */

/* Enhanced staggered animation for all dropdown levels */
.dropdown-menu li,
.sub-menu li {
    opacity: 0;
    transform: translateY(-5px);
    transition: all 0.2s ease;
}

.dropdown:hover .dropdown-menu li,
.dropdown.active .dropdown-menu li,
.dropdown-submenu:hover .dropdown-menu li,
.dropdown-submenu.active .dropdown-menu li,
.dropdown:hover .sub-menu li,
.dropdown.active .sub-menu li,
.dropdown-submenu:hover .sub-menu li,
.dropdown-submenu.active .sub-menu li {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered delays for smooth appearance */
.dropdown:hover .dropdown-menu li:nth-child(1),
.dropdown-submenu:hover .dropdown-menu li:nth-child(1),
.dropdown:hover .sub-menu li:nth-child(1),
.dropdown-submenu:hover .sub-menu li:nth-child(1) {
    transition-delay: 0.05s;
}

.dropdown:hover .dropdown-menu li:nth-child(2),
.dropdown-submenu:hover .dropdown-menu li:nth-child(2),
.dropdown:hover .sub-menu li:nth-child(2),
.dropdown-submenu:hover .sub-menu li:nth-child(2) {
    transition-delay: 0.1s;
}

.dropdown:hover .dropdown-menu li:nth-child(3),
.dropdown-submenu:hover .dropdown-menu li:nth-child(3),
.dropdown:hover .sub-menu li:nth-child(3),
.dropdown-submenu:hover .sub-menu li:nth-child(3) {
    transition-delay: 0.15s;
}

.dropdown:hover .dropdown-menu li:nth-child(4),
.dropdown-submenu:hover .dropdown-menu li:nth-child(4),
.dropdown:hover .sub-menu li:nth-child(4),
.dropdown-submenu:hover .sub-menu li:nth-child(4) {
    transition-delay: 0.2s;
}

.dropdown:hover .dropdown-menu li:nth-child(5),
.dropdown-submenu:hover .dropdown-menu li:nth-child(5),
.dropdown:hover .sub-menu li:nth-child(5),
.dropdown-submenu:hover .sub-menu li:nth-child(5) {
    transition-delay: 0.25s;
}

/* Visual indicator for items with submenus */
.menu-item-has-children > a {
    position: relative;
}

.menu-item-has-children.dropdown-submenu > a::after {
    content: '';
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid #6b7280;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    transition: border-color 0.2s ease;
}

.menu-item-has-children.dropdown-submenu:hover > a::after {
    border-left-color: #ec4899;
}
