/* Scholarship Frontend Custom Fields Styles */

/* Application Requirements Styles */
.application-requirements-list {
    margin: 20px 0;
}

.requirements-list {
    list-style: none;
    padding: 0;
    margin: 0;
    counter-reset: requirement-counter;
}

.requirement-item {
    counter-increment: requirement-counter;
    margin-bottom: 16px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #fa3c80;
    position: relative;
    transition: all 0.3s ease;
}

.requirement-item:hover {
    background: #f1f3f4;
    transform: translateX(4px);
}

.requirement-item::before {
    content: counter(requirement-counter);
    position: absolute;
    left: -12px;
    top: 16px;
    width: 24px;
    height: 24px;
    background: #fa3c80;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    box-shadow: 0 2px 4px rgba(250, 60, 128, 0.3);
}

.requirement-content {
    margin-left: 20px;
    line-height: 1.6;
    color: #374151;
}

.requirement-title {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.requirement-description {
    color: #374151;
    line-height: 1.6;
}

.requirement-description p {
    margin: 0 0 8px 0;
}

.requirement-description p:last-child {
    margin-bottom: 0;
}

/* Legacy support for old structure */
.requirement-content p {
    margin: 0 0 8px 0;
}

.requirement-content p:last-child {
    margin-bottom: 0;
}

/* Application Process Timeline Styles */
.application-process-timeline {
    margin: 20px 0;
    position: relative;
}

.application-process-timeline::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 32px;
    bottom: 32px;
    width: 2px;
    background: linear-gradient(to bottom, #004aad, #fa3c80);
    z-index: 1;
}

.process-step {
    position: relative;
    margin-bottom: 32px;
    padding-left: 56px;
}

.process-step:last-child {
    margin-bottom: 0;
}

.step-number {
    position: absolute;
    left: 0;
    top: 0;
    width: 32px;
    height: 32px;
    background: #004aad;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 74, 173, 0.3);
    transition: all 0.3s ease;
}

.process-step:last-child .step-number {
    background: #fa3c80;
    box-shadow: 0 2px 8px rgba(250, 60, 128, 0.3);
}

.process-step:hover .step-number {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 74, 173, 0.4);
}

.process-step:last-child:hover .step-number {
    box-shadow: 0 4px 12px rgba(250, 60, 128, 0.4);
}

.step-content {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    position: relative;
}

.step-content::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 12px;
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-right: 8px solid white;
    z-index: 3;
}

.process-step:hover .step-content {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.step-title {
    margin: 0 0 12px 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    line-height: 1.4;
}

.step-description {
    color: #6b7280;
    line-height: 1.6;
    margin: 0;
}

.step-description p {
    margin: 0 0 12px 0;
}

.step-description p:last-child {
    margin-bottom: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .requirement-item {
        margin-left: 0;
        border-left-width: 3px;
        padding: 12px;
    }
    
    .requirement-item::before {
        left: -10px;
        width: 20px;
        height: 20px;
        font-size: 10px;
        top: 12px;
    }
    
    .requirement-content {
        margin-left: 16px;
    }
    
    .application-process-timeline::before {
        left: 12px;
    }
    
    .process-step {
        padding-left: 44px;
        margin-bottom: 24px;
    }
    
    .step-number {
        width: 24px;
        height: 24px;
        font-size: 12px;
        left: 0;
    }
    
    .step-content {
        padding: 16px;
    }
    
    .step-content::before {
        left: -6px;
        border-right-width: 6px;
        border-top-width: 6px;
        border-bottom-width: 6px;
        top: 10px;
    }
    
    .step-title {
        font-size: 16px;
        margin-bottom: 8px;
    }
}

/* Animation for scroll reveal */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.requirement-item,
.process-step {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.requirement-item:nth-child(1) { animation-delay: 0.1s; }
.requirement-item:nth-child(2) { animation-delay: 0.2s; }
.requirement-item:nth-child(3) { animation-delay: 0.3s; }
.requirement-item:nth-child(4) { animation-delay: 0.4s; }
.requirement-item:nth-child(5) { animation-delay: 0.5s; }

.process-step:nth-child(1) { animation-delay: 0.1s; }
.process-step:nth-child(2) { animation-delay: 0.2s; }
.process-step:nth-child(3) { animation-delay: 0.3s; }
.process-step:nth-child(4) { animation-delay: 0.4s; }
.process-step:nth-child(5) { animation-delay: 0.5s; }

/* Print styles */
@media print {
    .requirement-item,
    .process-step {
        break-inside: avoid;
        page-break-inside: avoid;
    }
    
    .application-process-timeline::before {
        display: none;
    }
    
    .step-number {
        box-shadow: none;
        border: 2px solid #004aad;
    }
    
    .step-content {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .step-content::before {
        display: none;
    }
}

/* Dark mode support (if theme supports it) */
@media (prefers-color-scheme: dark) {
    .requirement-item {
        background: #374151;
        color: #f9fafb;
    }
    
    .requirement-content {
        color: #e5e7eb;
    }
    
    .step-content {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .step-title {
        color: #f9fafb;
    }
    
    .step-description {
        color: #d1d5db;
    }
}
