/**
 * Admin Styles for Nganh Hoc (Major/Field of Study)
 * 
 * @package IntHub
 * @since 1.0.0
 */

/* Meta Box Styling */
#nganh_hoc_details .form-table {
    margin-top: 1rem;
}

#nganh_hoc_details .form-table th {
    width: 200px;
    padding: 15px 10px 15px 0;
    vertical-align: top;
    font-weight: 600;
    color: #333;
}

#nganh_hoc_details .form-table td {
    padding: 15px 10px;
}

#nganh_hoc_details .form-table input[type="text"],
#nganh_hoc_details .form-table input[type="number"],
#nganh_hoc_details .form-table input[type="url"],
#nganh_hoc_details .form-table select,
#nganh_hoc_details .form-table textarea {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

#nganh_hoc_details .form-table input[type="text"]:focus,
#nganh_hoc_details .form-table input[type="number"]:focus,
#nganh_hoc_details .form-table input[type="url"]:focus,
#nganh_hoc_details .form-table select:focus,
#nganh_hoc_details .form-table textarea:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

#nganh_hoc_details .form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #666;
    font-size: 13px;
}

#nganh_hoc_details .form-table span[style*="color: red"] {
    color: #dc3232 !important;
    font-weight: bold;
}

/* Section Headers */
#nganh_hoc_details h3 {
    margin: 30px 0 15px 0;
    padding: 10px 0;
    border-bottom: 2px solid #0073aa;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

#nganh_hoc_details h3:first-of-type {
    margin-top: 15px;
}

/* Required Field Indicators */
.required-field {
    position: relative;
}

.required-field::after {
    content: " *";
    color: #dc3232;
    font-weight: bold;
}

/* Admin List Table Enhancements */
.wp-admin .wp-list-table .column-ma_nganh_hoc {
    width: 100px;
}

.wp-admin .wp-list-table .column-linh_vuc {
    width: 150px;
}

.wp-admin .wp-list-table .column-cap_do_dao_tao {
    width: 120px;
}

.wp-admin .wp-list-table .column-thoi_gian_dao_tao {
    width: 100px;
}

.wp-admin .wp-list-table .column-bang_cap {
    width: 120px;
}

/* Status Badges */
.nganh-hoc-status {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nganh-hoc-status.active {
    background-color: #46b450;
    color: white;
}

.nganh-hoc-status.inactive {
    background-color: #dc3232;
    color: white;
}

.nganh-hoc-status.draft {
    background-color: #ffb900;
    color: white;
}

/* Taxonomy Tags in Admin */
.admin-taxonomy-tag {
    display: inline-block;
    background: #f1f1f1;
    color: #333;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin: 0 2px 2px 0;
    text-decoration: none;
    border: 1px solid #ddd;
    transition: all 0.2s ease;
}

.admin-taxonomy-tag:hover {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
    text-decoration: none;
}

.admin-taxonomy-tag.linh-vuc {
    background: #e1f5fe;
    color: #0277bd;
    border-color: #81d4fa;
}

.admin-taxonomy-tag.cap-do {
    background: #f3e5f5;
    color: #7b1fa2;
    border-color: #ce93d8;
}

/* Filter Dropdowns */
.tablenav .actions select {
    margin-right: 5px;
    min-width: 150px;
}

/* Meta Box Icons */
.meta-box-icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 8px;
    vertical-align: middle;
    color: #0073aa;
}

/* Validation Messages */
.nganh-hoc-validation-error {
    background: #fff;
    border-left: 4px solid #dc3232;
    box-shadow: 0 1px 1px 0 rgba(0,0,0,.1);
    margin: 5px 15px 2px;
    padding: 1px 12px;
}

.nganh-hoc-validation-error p {
    margin: 0.5em 0;
    padding: 2px;
    color: #dc3232;
    font-weight: 600;
}

.nganh-hoc-validation-success {
    background: #fff;
    border-left: 4px solid #46b450;
    box-shadow: 0 1px 1px 0 rgba(0,0,0,.1);
    margin: 5px 15px 2px;
    padding: 1px 12px;
}

.nganh-hoc-validation-success p {
    margin: 0.5em 0;
    padding: 2px;
    color: #46b450;
    font-weight: 600;
}

/* Help Text Styling */
.help-text {
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
    font-size: 13px;
    line-height: 1.5;
}

.help-text .dashicons {
    color: #0073aa;
    margin-right: 5px;
}

/* Quick Edit Enhancements */
.quick-edit-row .inline-edit-col-left,
.quick-edit-row .inline-edit-col-center,
.quick-edit-row .inline-edit-col-right {
    float: left;
}

.quick-edit-row .inline-edit-col-left {
    width: 40%;
}

.quick-edit-row .inline-edit-col-center {
    width: 30%;
}

.quick-edit-row .inline-edit-col-right {
    width: 30%;
}

/* Bulk Edit Enhancements */
.bulk-edit-row .inline-edit-col {
    padding: 0 0.5em;
}

.bulk-edit-row .inline-edit-group {
    margin: 0 0 1em 0;
}

.bulk-edit-row .inline-edit-group label {
    display: block;
    margin: 0.2em 0;
    font-weight: 600;
}

/* Admin Menu Icon */
#adminmenu .menu-icon-nganh_hoc div.wp-menu-image:before {
    content: '\f501';
    font-family: dashicons;
}

/* Post States */
.post-state-nganh-hoc {
    background: #0073aa;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    margin-left: 5px;
}

/* Media Upload Enhancements */
.nganh-hoc-media-upload {
    margin: 10px 0;
}

.nganh-hoc-media-upload .button {
    margin-right: 10px;
}

.nganh-hoc-media-preview {
    margin-top: 10px;
    max-width: 300px;
}

.nganh-hoc-media-preview img {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Responsive Admin Styles */
@media screen and (max-width: 782px) {
    #nganh_hoc_details .form-table th,
    #nganh_hoc_details .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    #nganh_hoc_details .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
    
    #nganh_hoc_details .form-table input[type="text"],
    #nganh_hoc_details .form-table input[type="number"],
    #nganh_hoc_details .form-table input[type="url"],
    #nganh_hoc_details .form-table select,
    #nganh_hoc_details .form-table textarea {
        width: 100% !important;
        max-width: none;
    }
    
    .wp-admin .wp-list-table .column-ma_nganh_hoc,
    .wp-admin .wp-list-table .column-linh_vuc,
    .wp-admin .wp-list-table .column-cap_do_dao_tao,
    .wp-admin .wp-list-table .column-thoi_gian_dao_tao,
    .wp-admin .wp-list-table .column-bang_cap {
        width: auto;
    }
    
    .tablenav .actions select {
        min-width: 120px;
        margin-bottom: 5px;
    }
}

/* Print Styles */
@media print {
    #nganh_hoc_details .form-table input,
    #nganh_hoc_details .form-table select,
    #nganh_hoc_details .form-table textarea {
        border: 1px solid #000;
        background: white;
    }
    
    .nganh-hoc-validation-error,
    .nganh-hoc-validation-success {
        border: 1px solid #000;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .help-text {
        background: #2c3338;
        border-color: #3c434a;
        color: #c3c4c7;
    }
    
    .admin-taxonomy-tag {
        background: #3c434a;
        color: #c3c4c7;
        border-color: #50575e;
    }
    
    .admin-taxonomy-tag:hover {
        background: #2271b1;
        color: white;
        border-color: #2271b1;
    }
}

/* Accessibility Enhancements */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.nganh-hoc-field-required:focus {
    box-shadow: 0 0 0 2px #0073aa;
}

/* Loading States */
.nganh-hoc-loading {
    opacity: 0.6;
    pointer-events: none;
}

.nganh-hoc-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
