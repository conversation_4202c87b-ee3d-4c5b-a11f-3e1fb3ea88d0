/* University Taxonomy Styles */

/* Category Tags in University Cards */
.university-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 12px;
}

.university-categories .category-link,
.university-categories .category-item {
    display: inline-block;
    background: rgba(0, 74, 173, 0.1);
    color: #004aad;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.university-categories .category-link:hover {
    background: #004aad;
    color: white;
    transform: translateY(-1px);
}

/* Category Tags in Hero Section */
.hero-categories {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.hero-categories .category-tag {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Taxonomy Archive Page Styles */
.taxonomy-university_category .hero-section {
    background: linear-gradient(135deg, #004aad 0%, #fa3c80 100%);
    position: relative;
    overflow: hidden;
}

.taxonomy-university_category .hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Breadcrumb Styles */
.taxonomy-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    font-size: 14px;
}

.taxonomy-breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.taxonomy-breadcrumb a:hover {
    color: white;
}

.taxonomy-breadcrumb .separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 4px;
}

.taxonomy-breadcrumb .current {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

/* Filter Section Enhancements */
.filter-section {
    background: white;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-group label {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
}

.filter-group select,
.filter-group input {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #004aad;
    box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
}

/* Results Count */
.results-count {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #6b7280;
}

.results-count .count-number {
    font-weight: 600;
    color: #004aad;
}

/* University Cards Enhancements */
.university-card {
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.university-card:hover {
    border-color: #004aad;
    box-shadow: 0 10px 25px rgba(0, 74, 173, 0.15);
}

.university-card .category-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    margin-bottom: 12px;
}

.university-card .category-tag {
    background: rgba(0, 74, 173, 0.1);
    color: #004aad;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
}

/* No Results State */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
}

.no-results .icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    opacity: 0.5;
}

.no-results h3 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #374151;
}

.no-results p {
    font-size: 16px;
    line-height: 1.6;
}

/* Loading State */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #004aad;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 40px;
}

.pagination a,
.pagination span {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
    padding: 0 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination a {
    color: #374151;
    background: white;
}

.pagination a:hover {
    background: #004aad;
    color: white;
    border-color: #004aad;
}

.pagination .current {
    background: #004aad;
    color: white;
    border-color: #004aad;
}

.pagination .dots {
    border: none;
    background: none;
    color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
    .filter-section .container > div {
        flex-direction: column;
        gap: 16px;
    }
    
    .filter-section .flex-wrap {
        flex-direction: column;
        gap: 12px;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-group select,
    .filter-group input {
        width: 100%;
    }
    
    .hero-categories {
        justify-content: center;
    }
    
    .taxonomy-breadcrumb {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .university-card .category-tags {
        justify-content: center;
    }
}

@media (max-width: 640px) {
    .hero-categories .category-tag {
        font-size: 12px;
        padding: 4px 8px;
    }
    
    .university-card .category-tag {
        font-size: 10px;
        padding: 2px 4px;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .pagination a,
    .pagination span {
        min-width: 36px;
        height: 36px;
        font-size: 13px;
    }
}

/* Print Styles */
@media print {
    .filter-section,
    .pagination {
        display: none;
    }
    
    .university-card {
        break-inside: avoid;
        page-break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .hero-categories .category-tag {
        background: #f0f0f0;
        color: #333;
        border: 1px solid #ccc;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .filter-section {
        background: #1f2937;
        border-color: #374151;
    }
    
    .filter-group label {
        color: #f9fafb;
    }
    
    .filter-group select,
    .filter-group input {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .university-card {
        background: #1f2937;
        border-color: #374151;
    }
    
    .university-card .category-tag {
        background: rgba(59, 130, 246, 0.2);
        color: #93c5fd;
    }
    
    .no-results {
        color: #d1d5db;
    }
    
    .no-results h3 {
        color: #f9fafb;
    }
}
