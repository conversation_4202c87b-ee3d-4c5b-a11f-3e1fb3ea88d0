/**
 * <PERSON><PERSON><PERSON><PERSON> (Major/Field of Study) Styles
 * 
 * @package IntHub
 * @since 1.0.0
 */

/* Archive Page Styles */
.nganh-hoc-archive {
    padding: 2rem 0;
}

.nganh-hoc-archive .page-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.nganh-hoc-archive .page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.nganh-hoc-archive .page-description {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Filter Section */
.nganh-hoc-filters {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.nganh-hoc-filters h3 {
    margin-bottom: 1rem;
    color: #333;
    font-size: 1.3rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.filter-group {
    flex: 1;
    min-width: 200px;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.btn-filter,
.btn-reset {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-filter {
    background: #667eea;
    color: white;
}

.btn-filter:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-reset {
    background: #6c757d;
    color: white;
}

.btn-reset:hover {
    background: #5a6268;
}

/* Grid Layout */
.nganh-hoc-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

/* Card Styles */
.nganh-hoc-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.nganh-hoc-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.nganh-hoc-card .card-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    position: relative;
}

.nganh-hoc-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(255,255,255,0.3);
}

.nganh-hoc-card .card-title {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.nganh-hoc-card .card-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.nganh-hoc-card .card-body {
    padding: 1.5rem;
}

.nganh-hoc-card .card-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.nganh-hoc-card .card-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.detail-item .icon {
    width: 16px;
    height: 16px;
    color: #667eea;
}

.detail-item .label {
    font-weight: 600;
    color: #333;
}

.detail-item .value {
    color: #666;
}

.nganh-hoc-card .card-footer {
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.nganh-hoc-card .card-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.tag {
    background: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tag.linh-vuc {
    background: #d1ecf1;
    color: #0c5460;
}

.tag.cap-do {
    background: #d4edda;
    color: #155724;
}

.nganh-hoc-card .btn-read-more {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
    width: 100%;
}

.nganh-hoc-card .btn-read-more:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

/* Single Page Styles */
.single-nganh-hoc {
    padding: 2rem 0;
}

.nganh-hoc-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 3rem 0;
    border-radius: 15px;
    margin-bottom: 3rem;
    text-align: center;
}

.nganh-hoc-header .title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.nganh-hoc-header .meta-info {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    font-size: 1.1rem;
    opacity: 0.9;
}

.nganh-hoc-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
}

.main-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.sidebar-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.info-box {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.info-box h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.3rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.info-list {
    list-style: none;
    padding: 0;
}

.info-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-list li:last-child {
    border-bottom: none;
}

.info-list .label {
    font-weight: 600;
    color: #333;
}

.info-list .value {
    color: #666;
    text-align: right;
}

/* Content Sections */
.content-section {
    margin-bottom: 3rem;
}

.content-section h2 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 1rem;
    border-left: 4px solid #667eea;
    padding-left: 1rem;
}

.content-section h3 {
    color: #555;
    font-size: 1.4rem;
    margin-bottom: 1rem;
    margin-top: 2rem;
}

.content-section p {
    line-height: 1.7;
    color: #666;
    margin-bottom: 1rem;
}

.content-section ul,
.content-section ol {
    padding-left: 2rem;
    margin-bottom: 1rem;
}

.content-section li {
    line-height: 1.6;
    color: #666;
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nganh-hoc-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .filter-row {
        flex-direction: column;
    }
    
    .filter-group {
        min-width: 100%;
    }
    
    .nganh-hoc-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .nganh-hoc-header .title {
        font-size: 2rem;
    }
    
    .nganh-hoc-header .meta-info {
        flex-direction: column;
        gap: 1rem;
    }
    
    .card-details {
        grid-template-columns: 1fr !important;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 2rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
