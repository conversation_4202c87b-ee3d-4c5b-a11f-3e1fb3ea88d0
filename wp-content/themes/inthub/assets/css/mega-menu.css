/**
 * Mega Menu Styles for IntHub Theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

/* ==========================================================================
   Mega Menu Base Styles
   ========================================================================== */

.main-navigation {
    position: relative;
}

.navigation-wrapper {
    position: relative;
}

.menu-item {
    position: relative;
}

.has-mega-menu {
    position: static;
}

/* Header container adjustments for mega menu */
.header-main-container {
    position: relative;
    z-index: 50;
}

.site-header {
    position: sticky;
    top: 0;
    z-index: 50;
}

.mega-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #ffffff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-top: 3px solid #ec4899;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 0 0 8px 8px;
}

.has-mega-menu:hover .mega-menu,
.has-mega-menu.active .mega-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.mega-menu-content {
    padding: 2rem;
}

/* ==========================================================================
   Mega Menu Grid Layout
   ========================================================================== */

.mega-menu-grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.mega-menu-grid.columns-2 {
    grid-template-columns: repeat(2, 1fr);
}

.mega-menu-grid.columns-3 {
    grid-template-columns: repeat(3, 1fr);
}

.mega-menu-grid.columns-4 {
    grid-template-columns: repeat(4, 1fr);
}

.mega-menu-column {
    display: flex;
    flex-direction: column;
}

/* ==========================================================================
   Mega Menu Items
   ========================================================================== */

.mega-menu-item {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    border: 1px solid #e2e8f0;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.mega-menu-item:hover {
    background: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: #ec4899;
}

.mega-menu-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.mega-menu-item:hover .mega-menu-image {
    transform: scale(1.05);
}

.mega-menu-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 1rem;
    color: #ec4899;
}

.mega-menu-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.mega-menu-description {
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 1rem;
    line-height: 1.5;
    flex-grow: 1;
}

/* ==========================================================================
   Mega Menu Links
   ========================================================================== */

.mega-menu-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mega-menu-links li {
    margin-bottom: 0.5rem;
}

.mega-menu-links a {
    display: block;
    color: #475569;
    text-decoration: none;
    font-size: 0.875rem;
    padding: 0.25rem 0;
    transition: color 0.2s ease;
    border-left: 2px solid transparent;
    padding-left: 0.5rem;
}

.mega-menu-links a:hover {
    color: #ec4899;
    border-left-color: #ec4899;
}

.mega-menu-cta {
    display: inline-block;
    background: #ec4899;
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 1rem;
    transition: background 0.2s ease;
    text-align: center;
}

.mega-menu-cta:hover {
    background: #db2777;
    color: #ffffff;
}

/* ==========================================================================
   Animation Stagger Effect
   ========================================================================== */

.mega-menu-item {
    animation: fadeInUp 0.4s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.has-mega-menu:hover .mega-menu-item:nth-child(1) { animation-delay: 0.1s; }
.has-mega-menu:hover .mega-menu-item:nth-child(2) { animation-delay: 0.2s; }
.has-mega-menu:hover .mega-menu-item:nth-child(3) { animation-delay: 0.3s; }
.has-mega-menu:hover .mega-menu-item:nth-child(4) { animation-delay: 0.4s; }
.has-mega-menu:hover .mega-menu-item:nth-child(5) { animation-delay: 0.5s; }
.has-mega-menu:hover .mega-menu-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==========================================================================
   Featured Menu Item
   ========================================================================== */

.mega-menu-item.featured {
    background: linear-gradient(135deg, #ec4899 0%, #db2777 100%);
    color: #ffffff;
    grid-column: span 2;
}

.mega-menu-item.featured .mega-menu-title {
    color: #ffffff;
}

.mega-menu-item.featured .mega-menu-description {
    color: #fce7f3;
}

.mega-menu-item.featured .mega-menu-links a {
    color: #fce7f3;
}

.mega-menu-item.featured .mega-menu-links a:hover {
    color: #ffffff;
    border-left-color: #ffffff;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 1024px) {
    .mega-menu-grid.columns-4 {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .mega-menu-grid.columns-3 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .mega-menu {
        position: static;
        box-shadow: none;
        border: none;
        border-radius: 0;
        background: #f8fafc;
        opacity: 1;
        visibility: visible;
        transform: none;
        transition: none;
    }

    .mega-menu-content {
        padding: 1rem;
    }

    .mega-menu-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .mega-menu-item {
        padding: 1rem;
    }

    .mega-menu-image {
        height: 80px;
    }

    .mega-menu-item.featured {
        grid-column: span 1;
    }

    /* Mobile accordion style */
    .has-mega-menu .mega-menu {
        display: none;
    }

    .has-mega-menu.active .mega-menu {
        display: block;
    }

    /* Mobile menu toggle animations */
    .mobile-menu-toggle .hamburger-icon {
        display: block;
    }

    .mobile-menu-toggle .close-icon {
        display: none;
    }

    .mobile-menu-toggle[aria-expanded="true"] .hamburger-icon {
        display: none;
    }

    .mobile-menu-toggle[aria-expanded="true"] .close-icon {
        display: block;
    }

    /* Mobile navigation animations */
    .mobile-navigation {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }

    .mobile-navigation.show {
        max-height: 100vh;
    }

    /* Mobile menu list styling */
    .mobile-menu-list {
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .mobile-menu-list .menu-item {
        border-bottom: 1px solid #e5e7eb;
    }

    .mobile-menu-list .menu-item:last-child {
        border-bottom: none;
    }
}

@media (max-width: 480px) {
    .mega-menu-content {
        padding: 0.5rem;
    }
    
    .mega-menu-item {
        padding: 0.75rem;
    }
    
    .mega-menu-image {
        height: 60px;
    }
}

/* ==========================================================================
   Dark Mode Support
   ========================================================================== */

@media (prefers-color-scheme: dark) {
    .mega-menu {
        background: #1e293b;
        border-top-color: #ec4899;
    }
    
    .mega-menu-item {
        background: #334155;
        border-color: #475569;
    }
    
    .mega-menu-item:hover {
        background: #475569;
    }
    
    .mega-menu-title {
        color: #f1f5f9;
    }
    
    .mega-menu-description {
        color: #cbd5e1;
    }
    
    .mega-menu-links a {
        color: #e2e8f0;
    }
}
