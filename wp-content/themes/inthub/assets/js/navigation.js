/**
 * Navigation JavaScript for IntHub theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initNavigation();
    });

    /**
     * Initialize navigation functionality
     */
    function initNavigation() {
        initMobileMenu();
        initStickyHeader();
        initActiveMenuItems();
        initDropdownMenus();
    }

    /**
     * Initialize mobile menu
     */
    function initMobileMenu() {
        var $mobileToggle = $('.mobile-menu-toggle');
        var $mobileNav = $('#mobile-navigation');
        var $body = $('body');
        var isOpen = false;

        // Toggle mobile menu
        $mobileToggle.on('click', function(e) {
            e.preventDefault();
            toggleMobileMenu();
        });

        // Close mobile menu when clicking outside
        $(document).on('click', function(e) {
            if (isOpen && !$(e.target).closest('.site-header').length) {
                closeMobileMenu();
            }
        });

        // Close mobile menu on escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && isOpen) { // Escape key
                closeMobileMenu();
            }
        });

        // Close mobile menu when window is resized to desktop
        $(window).on('resize', function() {
            if ($(window).width() >= 768 && isOpen) {
                closeMobileMenu();
            }
        });

        // Handle mobile menu links
        $mobileNav.find('a').on('click', function() {
            // Close menu when clicking on anchor links
            if ($(this).attr('href').indexOf('#') !== -1) {
                setTimeout(closeMobileMenu, 300);
            }
        });

        function toggleMobileMenu() {
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        }

        function openMobileMenu() {
            isOpen = true;
            $mobileNav.removeClass('hidden').addClass('block');
            $mobileToggle.attr('aria-expanded', 'true');
            $body.addClass('mobile-menu-open');
            
            // Change hamburger to X
            updateMobileToggleIcon(true);
            
            // Animate menu items
            $mobileNav.find('li').each(function(index) {
                $(this).css({
                    'opacity': '0',
                    'transform': 'translateY(-10px)'
                }).delay(index * 50).animate({
                    'opacity': '1'
                }, 200, function() {
                    $(this).css('transform', 'translateY(0)');
                });
            });
        }

        function closeMobileMenu() {
            isOpen = false;
            $mobileNav.removeClass('block').addClass('hidden');
            $mobileToggle.attr('aria-expanded', 'false');
            $body.removeClass('mobile-menu-open');
            
            // Change X back to hamburger
            updateMobileToggleIcon(false);
        }

        function updateMobileToggleIcon(isOpen) {
            var $icon = $mobileToggle.find('svg');
            if (isOpen) {
                $icon.html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>');
            } else {
                $icon.html('<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>');
            }
        }
    }

    /**
     * Initialize sticky header
     */
    function initStickyHeader() {
        var $header = $('.site-header');
        var $window = $(window);
        var headerHeight = $header.outerHeight();
        var scrollThreshold = 100;
        var isSticky = false;

        function updateStickyHeader() {
            var scrollTop = $window.scrollTop();
            
            if (scrollTop > scrollThreshold && !isSticky) {
                isSticky = true;
                $header.addClass('header-sticky');
                $('body').css('padding-top', headerHeight + 'px');
            } else if (scrollTop <= scrollThreshold && isSticky) {
                isSticky = false;
                $header.removeClass('header-sticky');
                $('body').css('padding-top', '0');
            }
        }

        // Throttle scroll events for better performance
        var throttledUpdate = throttle(updateStickyHeader, 10);
        $window.on('scroll', throttledUpdate);

        // Update on resize
        $window.on('resize', function() {
            headerHeight = $header.outerHeight();
            if (isSticky) {
                $('body').css('padding-top', headerHeight + 'px');
            }
        });

        // Add CSS for sticky header
        if (!$('#sticky-header-styles').length) {
            $('<style id="sticky-header-styles">')
                .text(`
                    .site-header {
                        transition: all 0.3s ease;
                    }
                    .site-header.header-sticky {
                        box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
                        backdrop-filter: blur(10px);
                        background: rgba(255, 255, 255, 0.95);
                    }
                    body.mobile-menu-open {
                        overflow: hidden;
                    }
                `)
                .appendTo('head');
        }
    }

    /**
     * Initialize active menu items
     */
    function initActiveMenuItems() {
        var currentUrl = window.location.href;
        var currentPath = window.location.pathname;

        // Mark current page menu item as active
        $('.main-navigation a, #mobile-navigation a').each(function() {
            var $link = $(this);
            var href = $link.attr('href');

            if (href === currentUrl || href === currentPath || 
                (href !== '#' && currentUrl.indexOf(href) !== -1)) {
                $link.addClass('current-menu-item');
                $link.closest('li').addClass('current-menu-item');
            }
        });

        // Handle section highlighting for single page navigation
        if ($('body').hasClass('home-page')) {
            initSectionHighlighting();
        }
    }

    /**
     * Initialize section highlighting for single page navigation
     */
    function initSectionHighlighting() {
        var $sections = $('section[id]');
        var $navLinks = $('.main-navigation a[href*="#"], #mobile-navigation a[href*="#"]');
        var headerHeight = $('.site-header').outerHeight() || 0;

        function updateActiveSection() {
            var scrollTop = $(window).scrollTop() + headerHeight + 50;
            var currentSection = '';

            $sections.each(function() {
                var $section = $(this);
                var sectionTop = $section.offset().top;
                var sectionBottom = sectionTop + $section.outerHeight();

                if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
                    currentSection = $section.attr('id');
                }
            });

            // Update active nav links
            $navLinks.removeClass('active-section');
            if (currentSection) {
                $navLinks.filter('[href*="#' + currentSection + '"]').addClass('active-section');
            }
        }

        // Throttle scroll events
        var throttledUpdate = throttle(updateActiveSection, 100);
        $(window).on('scroll', throttledUpdate);

        // Initial update
        updateActiveSection();
    }

    /**
     * Initialize enhanced multi-level dropdown menus
     */
    function initDropdownMenus() {
        var $dropdownItems = $('.menu-item-has-children');
        var $dropdownToggles = $('.menu-item-has-children > a');
        var hoverTimeouts = {};
        var isMobile = false;

        // Check if mobile
        function checkMobile() {
            isMobile = $(window).width() < 768;
        }

        checkMobile();

        // Initialize each dropdown item
        $dropdownItems.each(function() {
            var $item = $(this);
            var $toggle = $item.children('a');
            var $submenu = $item.children('.sub-menu, .dropdown-menu');
            var itemId = $item.attr('id') || 'menu-item-' + Math.random().toString(36).substr(2, 9);

            // Ensure item has an ID for timeout management
            if (!$item.attr('id')) {
                $item.attr('id', itemId);
            }

            if ($submenu.length) {
                // Add ARIA attributes for accessibility
                $toggle.attr({
                    'aria-haspopup': 'true',
                    'aria-expanded': 'false'
                });

                $submenu.attr('aria-hidden', 'true');

                // Position submenu for screen edge detection
                if (!isMobile) {
                    positionSubmenu($item, $submenu);
                }
            }
        });

        // Desktop hover behavior with proper multi-level support
        $dropdownItems.on('mouseenter', function(e) {
            if (!isMobile) {
                var $item = $(this);
                var $submenu = $item.children('.sub-menu, .dropdown-menu');
                var $toggle = $item.children('a');
                var itemId = $item.attr('id');

                // Clear any existing timeout for this item
                if (hoverTimeouts[itemId]) {
                    clearTimeout(hoverTimeouts[itemId]);
                    delete hoverTimeouts[itemId];
                }

                // Don't close parent dropdowns when entering child items
                e.stopPropagation();

                // Close sibling dropdowns at the same level
                $item.siblings('.dropdown.active').each(function() {
                    var $sibling = $(this);
                    var $siblingToggle = $sibling.children('a');
                    var $siblingSubmenu = $sibling.children('.sub-menu, .dropdown-menu');

                    $sibling.removeClass('active');
                    $siblingToggle.attr('aria-expanded', 'false');
                    $siblingSubmenu.attr('aria-hidden', 'true');

                    // Also close any nested dropdowns in siblings
                    $sibling.find('.dropdown.active').removeClass('active')
                        .children('a').attr('aria-expanded', 'false')
                        .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
                });

                // Show current dropdown
                $item.addClass('active');
                $toggle.attr('aria-expanded', 'true');
                $submenu.attr('aria-hidden', 'false');

                // Reposition if needed
                positionSubmenu($item, $submenu);
            }
        }).on('mouseleave', function(e) {
            if (!isMobile) {
                var $item = $(this);
                var $toggle = $item.children('a');
                var $submenu = $item.children('.sub-menu, .dropdown-menu');
                var itemId = $item.attr('id');

                // Don't close if moving to a child element
                var $relatedTarget = $(e.relatedTarget);
                if ($relatedTarget.closest($item).length > 0) {
                    return;
                }

                // Set timeout to close dropdown
                hoverTimeouts[itemId] = setTimeout(function() {
                    $item.removeClass('active');
                    $toggle.attr('aria-expanded', 'false');
                    $submenu.attr('aria-hidden', 'true');

                    // Close all nested dropdowns
                    $item.find('.dropdown.active').removeClass('active')
                        .children('a').attr('aria-expanded', 'false')
                        .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');

                    delete hoverTimeouts[itemId];
                }, 200);
            }
        });

        // Mobile click behavior for all levels
        $dropdownToggles.on('click', function(e) {
            if (isMobile) {
                var $toggle = $(this);
                var $item = $toggle.parent();
                var $submenu = $item.children('.sub-menu, .dropdown-menu');

                // Only prevent default if this item has a submenu
                if ($submenu.length > 0) {
                    e.preventDefault();
                    e.stopPropagation();

                    var isActive = $item.hasClass('active');

                    if (isActive) {
                        // Close dropdown and all nested dropdowns
                        $item.removeClass('active');
                        $toggle.attr('aria-expanded', 'false');
                        $submenu.attr('aria-hidden', 'true').slideUp(200);

                        // Close all nested dropdowns
                        $item.find('.dropdown.active').removeClass('active')
                            .children('a').attr('aria-expanded', 'false')
                            .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true').slideUp(200);
                    } else {
                        // Close sibling dropdowns at the same level
                        $item.siblings('.dropdown.active').removeClass('active')
                            .children('a').attr('aria-expanded', 'false')
                            .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true').slideUp(200);

                        // Close nested dropdowns in siblings
                        $item.siblings().find('.dropdown.active').removeClass('active')
                            .children('a').attr('aria-expanded', 'false')
                            .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true').slideUp(200);

                        // Open current dropdown
                        $item.addClass('active');
                        $toggle.attr('aria-expanded', 'true');
                        $submenu.attr('aria-hidden', 'false').slideDown(200);
                    }
                }
            }
        });

        // Enhanced keyboard navigation for all levels
        $dropdownToggles.on('keydown', function(e) {
            var $toggle = $(this);
            var $item = $toggle.parent();
            var $submenu = $item.children('.sub-menu, .dropdown-menu');
            var $parentMenu = $item.closest('.sub-menu, .dropdown-menu');
            var $parentItem = $parentMenu.parent('.menu-item-has-children');

            switch(e.keyCode) {
                case 13: // Enter
                case 32: // Space
                    if ($submenu.length) {
                        e.preventDefault();
                        if (!isMobile) {
                            // Desktop: Open dropdown and focus first item
                            $item.addClass('active');
                            $toggle.attr('aria-expanded', 'true');
                            $submenu.attr('aria-hidden', 'false');

                            // Focus first submenu item
                            setTimeout(function() {
                                $submenu.find('a').first().focus();
                            }, 50);
                        } else {
                            // Mobile: Toggle dropdown
                            $toggle.trigger('click');
                        }
                    }
                    break;

                case 27: // Escape
                    e.preventDefault();
                    if ($item.hasClass('active')) {
                        // Close current dropdown
                        $item.removeClass('active');
                        $toggle.attr('aria-expanded', 'false');
                        $submenu.attr('aria-hidden', 'true');
                        $toggle.focus();
                    } else if ($parentItem.length) {
                        // If in a submenu, go back to parent
                        var $parentToggle = $parentItem.children('a');
                        $parentItem.removeClass('active');
                        $parentToggle.attr('aria-expanded', 'false').focus();
                        $parentMenu.attr('aria-hidden', 'true');
                    }
                    break;

                case 40: // Down arrow
                    if ($submenu.length && !$item.hasClass('active')) {
                        // Open dropdown if not already open
                        e.preventDefault();
                        $item.addClass('active');
                        $toggle.attr('aria-expanded', 'true');
                        $submenu.attr('aria-hidden', 'false');

                        setTimeout(function() {
                            $submenu.find('a').first().focus();
                        }, 50);
                    } else if ($submenu.length && $item.hasClass('active')) {
                        // Focus first submenu item
                        e.preventDefault();
                        $submenu.find('a').first().focus();
                    }
                    break;

                case 39: // Right arrow
                    if ($submenu.length) {
                        e.preventDefault();
                        if (!$item.hasClass('active')) {
                            $item.addClass('active');
                            $toggle.attr('aria-expanded', 'true');
                            $submenu.attr('aria-hidden', 'false');
                        }
                        setTimeout(function() {
                            $submenu.find('a').first().focus();
                        }, 50);
                    }
                    break;

                case 37: // Left arrow
                    if ($parentItem.length) {
                        e.preventDefault();
                        var $parentToggle = $parentItem.children('a');
                        $item.removeClass('active');
                        $toggle.attr('aria-expanded', 'false');
                        $submenu.attr('aria-hidden', 'true');
                        $parentToggle.focus();
                    }
                    break;
            }
        });

        // Enhanced submenu keyboard navigation for all levels
        $(document).on('keydown', '.sub-menu a, .dropdown-menu a', function(e) {
            var $link = $(this);
            var $currentItem = $link.parent('.menu-item-has-children');
            var $submenu = $link.closest('.sub-menu, .dropdown-menu');
            var $parentItem = $submenu.parent('.menu-item-has-children');
            var $links = $submenu.children('li').children('a');
            var currentIndex = $links.index($link);

            switch(e.keyCode) {
                case 27: // Escape
                    e.preventDefault();
                    if ($parentItem.length) {
                        var $parentToggle = $parentItem.children('a');
                        $parentItem.removeClass('active');
                        $parentToggle.attr('aria-expanded', 'false').focus();
                        $submenu.attr('aria-hidden', 'true');

                        // Close all nested dropdowns
                        $parentItem.find('.dropdown.active').removeClass('active')
                            .children('a').attr('aria-expanded', 'false')
                            .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
                    }
                    break;

                case 38: // Up arrow
                    e.preventDefault();
                    if (currentIndex > 0) {
                        $links.eq(currentIndex - 1).focus();
                    } else {
                        // Go to parent toggle
                        if ($parentItem.length) {
                            $parentItem.children('a').focus();
                        }
                    }
                    break;

                case 40: // Down arrow
                    e.preventDefault();
                    if (currentIndex < $links.length - 1) {
                        $links.eq(currentIndex + 1).focus();
                    }
                    break;

                case 39: // Right arrow
                    if ($currentItem && $currentItem.length) {
                        var $childSubmenu = $currentItem.children('.sub-menu, .dropdown-menu');
                        if ($childSubmenu.length) {
                            e.preventDefault();
                            $currentItem.addClass('active');
                            $link.attr('aria-expanded', 'true');
                            $childSubmenu.attr('aria-hidden', 'false');

                            setTimeout(function() {
                                $childSubmenu.find('a').first().focus();
                            }, 50);
                        }
                    }
                    break;

                case 37: // Left arrow
                    e.preventDefault();
                    if ($parentItem.length) {
                        var $parentToggle = $parentItem.children('a');

                        // Close current level if it's a dropdown item
                        if ($currentItem && $currentItem.length) {
                            $currentItem.removeClass('active');
                            $link.attr('aria-expanded', 'false');
                            $currentItem.children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
                        }

                        $parentToggle.focus();
                    }
                    break;
            }
        });

        // Close dropdowns when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.menu-item-has-children').length) {
                $('.menu-item-has-children.active').removeClass('active')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
            }
        });

        // Enhanced positioning function for multi-level dropdowns
        function positionSubmenu($item, $submenu) {
            if (!isMobile && $submenu.length) {
                var itemOffset = $item.offset();
                var submenuWidth = $submenu.outerWidth();
                var windowWidth = $(window).width();
                var isSubSubmenu = $item.closest('.sub-menu, .dropdown-menu').length > 0;
                var scrollLeft = $(window).scrollLeft();

                // Reset positioning classes
                $item.removeClass('dropdown-right dropdown-left');

                if (isSubSubmenu) {
                    // For sub-submenus, check if they would go off the right edge
                    var rightEdge = itemOffset.left + $item.outerWidth() + submenuWidth;
                    if (rightEdge > windowWidth + scrollLeft - 20) {
                        $item.addClass('dropdown-left');
                    }
                } else {
                    // For main dropdowns, check if they would go off the right edge
                    var rightEdge = itemOffset.left + submenuWidth;
                    if (rightEdge > windowWidth + scrollLeft - 20) {
                        $item.addClass('dropdown-right');
                    }
                }
            }
        }

        // Enhanced window resize handler
        $(window).on('resize', function() {
            var wasMobile = isMobile;
            checkMobile();

            // Clear all timeouts when switching between mobile/desktop
            if (wasMobile !== isMobile) {
                Object.keys(hoverTimeouts).forEach(function(key) {
                    clearTimeout(hoverTimeouts[key]);
                    delete hoverTimeouts[key];
                });
            }

            if (!isMobile) {
                // Desktop mode: reposition active dropdowns
                $('.menu-item-has-children.active').each(function() {
                    var $item = $(this);
                    var $submenu = $item.children('.sub-menu, .dropdown-menu');
                    positionSubmenu($item, $submenu);
                });
            } else {
                // Mobile mode: reset all dropdown states
                $('.menu-item-has-children').removeClass('active dropdown-right dropdown-left')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true').removeAttr('style');
            }
        });

        // Close dropdowns when clicking outside
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.menu-item-has-children').length) {
                $('.menu-item-has-children.active').removeClass('active')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');

                // Clear all hover timeouts
                Object.keys(hoverTimeouts).forEach(function(key) {
                    clearTimeout(hoverTimeouts[key]);
                    delete hoverTimeouts[key];
                });
            }
        });
    }

    /**
     * Utility function to throttle events
     */
    function throttle(func, limit) {
        var inThrottle;
        return function() {
            var args = arguments;
            var context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(function() {
                    inThrottle = false;
                }, limit);
            }
        };
    }

    // Expose enhanced navigation functions globally
    window.IntHubNavigation = {
        closeMobileMenu: function() {
            $('.mobile-menu-toggle').trigger('click');
        },
        closeAllDropdowns: function() {
            $('.menu-item-has-children.active').removeClass('active')
                .children('a').attr('aria-expanded', 'false')
                .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
        },
        openDropdown: function($item) {
            if ($item.hasClass('menu-item-has-children')) {
                var $toggle = $item.children('a');
                var $submenu = $item.children('.sub-menu, .dropdown-menu');

                // Close siblings first
                $item.siblings('.dropdown.active').removeClass('active')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');

                $item.addClass('active');
                $toggle.attr('aria-expanded', 'true');
                $submenu.attr('aria-hidden', 'false');
            }
        },
        closeDropdown: function($item) {
            if ($item.hasClass('menu-item-has-children')) {
                var $toggle = $item.children('a');
                var $submenu = $item.children('.sub-menu, .dropdown-menu');

                $item.removeClass('active');
                $toggle.attr('aria-expanded', 'false');
                $submenu.attr('aria-hidden', 'true');

                // Close all nested dropdowns
                $item.find('.dropdown.active').removeClass('active')
                    .children('a').attr('aria-expanded', 'false')
                    .end().children('.sub-menu, .dropdown-menu').attr('aria-hidden', 'true');
            }
        },
        isMobile: function() {
            return $(window).width() < 768;
        }
    };

})(jQuery);
