/**
 * Admin JavaScript for Nganh Hoc (Major/Field of Study)
 * 
 * @package IntHub
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize Admin Nganh Hoc functionality
    var NganhHocAdmin = {
        
        /**
         * Initialize all functions
         */
        init: function() {
            this.initValidation();
            this.initCharacterCounters();
            this.initConditionalFields();
            this.initBulkActions();
            this.initQuickEdit();
            this.initMediaUpload();
            this.initTooltips();
        },

        /**
         * Initialize form validation
         */
        initValidation: function() {
            var self = this;
            
            // Real-time validation
            $('#nganh_hoc_details input[required], #nganh_hoc_details select[required]').on('blur', function() {
                self.validateField($(this));
            });

            // Form submission validation
            $('#post').on('submit', function(e) {
                var isValid = self.validateForm();
                if (!isValid) {
                    e.preventDefault();
                    self.showValidationMessage(inthub_admin_ajax.strings.validation_error, 'error');
                    $('html, body').animate({
                        scrollTop: $('.nganh-hoc-validation-error').first().offset().top - 100
                    }, 500);
                }
            });
        },

        /**
         * Validate individual field
         */
        validateField: function($field) {
            var isValid = true;
            var value = $field.val().trim();
            var fieldName = $field.attr('name');
            
            // Remove existing error styling
            $field.removeClass('error');
            $field.next('.error-message').remove();
            
            // Required field validation
            if ($field.attr('required') && !value) {
                isValid = false;
                this.showFieldError($field, 'Trường này là bắt buộc.');
            }
            
            // Specific field validations
            switch (fieldName) {
                case 'ma_nganh_hoc':
                    if (value && !/^[A-Z0-9]{2,10}$/.test(value)) {
                        isValid = false;
                        this.showFieldError($field, 'Mã ngành học phải từ 2-10 ký tự, chỉ chứa chữ hoa và số.');
                    }
                    break;
                    
                case 'thoi_gian_dao_tao':
                    if (value && !/^\d+(\.\d+)?\s*(năm|tháng|year|month)s?$/i.test(value)) {
                        isValid = false;
                        this.showFieldError($field, 'Định dạng: "4 năm" hoặc "2.5 năm".');
                    }
                    break;
            }
            
            return isValid;
        },

        /**
         * Validate entire form
         */
        validateForm: function() {
            var self = this;
            var isValid = true;
            
            // Clear previous validation messages
            $('.nganh-hoc-validation-error').remove();
            
            // Validate required fields
            $('#nganh_hoc_details input[required], #nganh_hoc_details select[required]').each(function() {
                if (!self.validateField($(this))) {
                    isValid = false;
                }
            });
            
            // Check for duplicate ma_nganh_hoc
            var maNganh = $('#ma_nganh_hoc').val().trim();
            if (maNganh) {
                // This would typically involve an AJAX call to check for duplicates
                // For now, we'll just validate the format
            }
            
            return isValid;
        },

        /**
         * Show field-specific error
         */
        showFieldError: function($field, message) {
            $field.addClass('error');
            $field.after('<div class="error-message" style="color: #dc3232; font-size: 12px; margin-top: 3px;">' + message + '</div>');
        },

        /**
         * Show validation message
         */
        showValidationMessage: function(message, type) {
            var className = type === 'error' ? 'nganh-hoc-validation-error' : 'nganh-hoc-validation-success';
            var $message = $('<div class="' + className + '"><p>' + message + '</p></div>');
            
            // Remove existing messages
            $('.nganh-hoc-validation-error, .nganh-hoc-validation-success').remove();
            
            // Add new message
            $('#nganh_hoc_details').prepend($message);
            
            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(function() {
                    $message.fadeOut();
                }, 3000);
            }
        },

        /**
         * Initialize character counters
         */
        initCharacterCounters: function() {
            // Add character counters to text fields with limits
            var fieldsWithLimits = {
                'ma_nganh_hoc': 10,
                'thoi_gian_dao_tao': 50,
                'bang_cap': 50,
                'hoc_phi_uoc_tinh': 100
            };
            
            $.each(fieldsWithLimits, function(fieldName, limit) {
                var $field = $('input[name="' + fieldName + '"]');
                if ($field.length) {
                    var $counter = $('<div class="char-counter" style="font-size: 11px; color: #666; margin-top: 3px;">0/' + limit + '</div>');
                    $field.after($counter);
                    
                    $field.on('input', function() {
                        var length = $(this).val().length;
                        $counter.text(length + '/' + limit);
                        
                        if (length > limit) {
                            $counter.css('color', '#dc3232');
                            $(this).addClass('over-limit');
                        } else {
                            $counter.css('color', '#666');
                            $(this).removeClass('over-limit');
                        }
                    });
                    
                    // Trigger initial count
                    $field.trigger('input');
                }
            });
        },

        /**
         * Initialize conditional fields
         */
        initConditionalFields: function() {
            // Show/hide fields based on selections
            $('#bang_cap').on('change', function() {
                var value = $(this).val();
                var $timeField = $('#thoi_gian_dao_tao').closest('tr');
                
                // Suggest typical durations based on degree type
                var suggestions = {
                    'cao-dang': '2-3 năm',
                    'cu-nhan': '4 năm',
                    'ky-su': '4-5 năm',
                    'bac-si': '6 năm',
                    'duoc-si': '5 năm',
                    'thac-si': '1.5-2 năm',
                    'tien-si': '3-4 năm',
                    'chung-chi': '6 tháng - 2 năm'
                };
                
                if (suggestions[value] && !$('#thoi_gian_dao_tao').val()) {
                    $('#thoi_gian_dao_tao').val(suggestions[value]);
                }
            });
        },

        /**
         * Initialize bulk actions
         */
        initBulkActions: function() {
            // Add custom bulk actions
            $('<option>').val('update_linh_vuc').text('Cập nhật lĩnh vực').appendTo('select[name="action"]');
            $('<option>').val('update_cap_do').text('Cập nhật cấp độ').appendTo('select[name="action"]');
            
            // Handle bulk action form submission
            $('#posts-filter').on('submit', function(e) {
                var action = $('select[name="action"]').val();
                
                if (action === 'update_linh_vuc' || action === 'update_cap_do') {
                    e.preventDefault();
                    NganhHocAdmin.showBulkEditModal(action);
                }
            });
        },

        /**
         * Show bulk edit modal
         */
        showBulkEditModal: function(action) {
            var selectedPosts = [];
            $('input[name="post[]"]:checked').each(function() {
                selectedPosts.push($(this).val());
            });
            
            if (selectedPosts.length === 0) {
                alert('Vui lòng chọn ít nhất một ngành học.');
                return;
            }
            
            // Create modal (simplified version)
            var modalContent = '<div id="bulk-edit-modal" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border: 1px solid #ccc; z-index: 9999;">';
            modalContent += '<h3>Cập nhật hàng loạt</h3>';
            modalContent += '<p>Đã chọn ' + selectedPosts.length + ' ngành học</p>';
            modalContent += '<button type="button" id="close-bulk-modal">Đóng</button>';
            modalContent += '</div>';
            modalContent += '<div id="bulk-modal-overlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9998;"></div>';
            
            $('body').append(modalContent);
            
            $('#close-bulk-modal, #bulk-modal-overlay').on('click', function() {
                $('#bulk-edit-modal, #bulk-modal-overlay').remove();
            });
        },

        /**
         * Initialize quick edit functionality
         */
        initQuickEdit: function() {
            // Enhance quick edit with custom fields
            $('.editinline').on('click', function() {
                var postId = $(this).closest('tr').attr('id').replace('post-', '');
                var $row = $('#edit-' + postId);
                
                // Add custom fields to quick edit
                setTimeout(function() {
                    if ($row.find('.nganh-hoc-quick-edit').length === 0) {
                        var quickEditHtml = '<fieldset class="inline-edit-col-right nganh-hoc-quick-edit">';
                        quickEditHtml += '<div class="inline-edit-col">';
                        quickEditHtml += '<label><span class="title">Mã ngành học</span><input type="text" name="ma_nganh_hoc" value=""></label>';
                        quickEditHtml += '<label><span class="title">Thời gian đào tạo</span><input type="text" name="thoi_gian_dao_tao" value=""></label>';
                        quickEditHtml += '</div>';
                        quickEditHtml += '</fieldset>';
                        
                        $row.find('.inline-edit-col-right').after(quickEditHtml);
                    }
                }, 100);
            });
        },

        /**
         * Initialize media upload
         */
        initMediaUpload: function() {
            // Custom media upload buttons
            $('.nganh-hoc-media-upload').on('click', function(e) {
                e.preventDefault();
                
                var $button = $(this);
                var targetField = $button.data('target');
                
                var mediaUploader = wp.media({
                    title: 'Chọn hình ảnh',
                    button: {
                        text: 'Sử dụng hình ảnh này'
                    },
                    multiple: false
                });
                
                mediaUploader.on('select', function() {
                    var attachment = mediaUploader.state().get('selection').first().toJSON();
                    $('#' + targetField).val(attachment.url);
                    $button.siblings('.media-preview').html('<img src="' + attachment.url + '" style="max-width: 200px;">');
                });
                
                mediaUploader.open();
            });
        },

        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Add tooltips to help icons
            $('.help-icon').hover(
                function() {
                    var helpText = $(this).data('help');
                    var $tooltip = $('<div class="admin-tooltip">' + helpText + '</div>');
                    $('body').append($tooltip);
                    
                    var offset = $(this).offset();
                    $tooltip.css({
                        top: offset.top - $tooltip.outerHeight() - 10,
                        left: offset.left + ($(this).outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                    }).fadeIn(200);
                },
                function() {
                    $('.admin-tooltip').fadeOut(200, function() {
                        $(this).remove();
                    });
                }
            );
        },

        /**
         * Auto-save functionality
         */
        autoSave: function() {
            var formData = $('#post').serialize();
            
            $.ajax({
                url: inthub_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'nganh_hoc_auto_save',
                    nonce: inthub_admin_ajax.nonce,
                    form_data: formData
                },
                success: function(response) {
                    if (response.success) {
                        $('.auto-save-indicator').text('Đã lưu tự động lúc ' + new Date().toLocaleTimeString());
                    }
                }
            });
        }
    };

    // Initialize when document is ready
    NganhHocAdmin.init();

    // Auto-save every 2 minutes
    if ($('#post').length) {
        setInterval(function() {
            NganhHocAdmin.autoSave();
        }, 120000);
    }

    // Add some CSS for admin enhancements
    var adminStyles = `
        <style>
        .error { border-color: #dc3232 !important; }
        .over-limit { border-color: #dc3232 !important; background-color: #ffeaea; }
        .char-counter { font-size: 11px; color: #666; margin-top: 3px; }
        .admin-tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10000;
            max-width: 250px;
            display: none;
        }
        .admin-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
        .help-icon {
            cursor: help;
            color: #666;
            margin-left: 5px;
        }
        .auto-save-indicator {
            font-size: 11px;
            color: #666;
            font-style: italic;
            margin-left: 10px;
        }
        </style>
    `;
    
    $('head').append(adminStyles);

    // Expose NganhHocAdmin object globally
    window.NganhHocAdmin = NganhHocAdmin;
});
