/**
 * <PERSON><PERSON><PERSON> (Major/Field of Study) JavaScript
 * 
 * @package IntHub
 * @since 1.0.0
 */

jQuery(document).ready(function($) {
    'use strict';

    // Initialize Nganh Hoc functionality
    var NganhHoc = {
        
        /**
         * Initialize all functions
         */
        init: function() {
            this.initFilters();
            this.initSearch();
            this.initLoadMore();
            this.initAnimations();
            this.initTooltips();
        },

        /**
         * Initialize filter functionality
         */
        initFilters: function() {
            var self = this;
            
            // Filter form submission
            $('.nganh-hoc-filters form').on('submit', function(e) {
                e.preventDefault();
                self.applyFilters();
            });

            // Filter button click
            $('.btn-filter').on('click', function(e) {
                e.preventDefault();
                self.applyFilters();
            });

            // Reset button click
            $('.btn-reset').on('click', function(e) {
                e.preventDefault();
                self.resetFilters();
            });

            // Auto-filter on select change
            $('.nganh-hoc-filters select').on('change', function() {
                self.applyFilters();
            });
        },

        /**
         * Apply filters to the grid
         */
        applyFilters: function() {
            var $grid = $('.nganh-hoc-grid');
            var $cards = $('.nganh-hoc-card');
            
            // Get filter values
            var linhVuc = $('#filter-linh-vuc').val();
            var capDo = $('#filter-cap-do').val();
            var searchTerm = $('#filter-search').val().toLowerCase();

            // Show loading state
            $grid.addClass('loading');

            // Filter cards
            $cards.each(function() {
                var $card = $(this);
                var show = true;

                // Check linh vuc filter
                if (linhVuc && !$card.hasClass('linh-vuc-' + linhVuc)) {
                    show = false;
                }

                // Check cap do filter
                if (capDo && !$card.hasClass('cap-do-' + capDo)) {
                    show = false;
                }

                // Check search term
                if (searchTerm) {
                    var cardText = $card.text().toLowerCase();
                    if (cardText.indexOf(searchTerm) === -1) {
                        show = false;
                    }
                }

                // Show/hide card with animation
                if (show) {
                    $card.fadeIn(300);
                } else {
                    $card.fadeOut(300);
                }
            });

            // Remove loading state
            setTimeout(function() {
                $grid.removeClass('loading');
            }, 300);

            // Update results count
            this.updateResultsCount();
        },

        /**
         * Reset all filters
         */
        resetFilters: function() {
            var $grid = $('.nganh-hoc-grid');
            var $cards = $('.nganh-hoc-card');

            // Reset form fields
            $('.nganh-hoc-filters select').val('');
            $('.nganh-hoc-filters input[type="text"]').val('');

            // Show all cards
            $grid.addClass('loading');
            $cards.fadeIn(300);

            setTimeout(function() {
                $grid.removeClass('loading');
            }, 300);

            // Update results count
            this.updateResultsCount();
        },

        /**
         * Initialize search functionality
         */
        initSearch: function() {
            var self = this;
            var searchTimeout;

            $('#filter-search').on('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function() {
                    self.applyFilters();
                }, 300);
            });
        },

        /**
         * Initialize load more functionality
         */
        initLoadMore: function() {
            var $loadMoreBtn = $('.btn-load-more');
            
            if ($loadMoreBtn.length) {
                $loadMoreBtn.on('click', function(e) {
                    e.preventDefault();
                    
                    var $btn = $(this);
                    var page = parseInt($btn.data('page')) || 1;
                    var maxPages = parseInt($btn.data('max-pages')) || 1;
                    
                    if (page >= maxPages) {
                        return;
                    }
                    
                    // Show loading state
                    $btn.addClass('loading').text('Đang tải...');
                    
                    // AJAX request to load more posts
                    $.ajax({
                        url: inthub_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'load_more_nganh_hoc',
                            page: page + 1,
                            nonce: inthub_ajax.nonce
                        },
                        success: function(response) {
                            if (response.success && response.data.html) {
                                $('.nganh-hoc-grid').append(response.data.html);
                                $btn.data('page', page + 1);
                                
                                // Initialize animations for new cards
                                NganhHoc.initAnimations();
                                
                                // Hide button if no more pages
                                if (page + 1 >= maxPages) {
                                    $btn.fadeOut();
                                }
                            }
                        },
                        error: function() {
                            alert('Có lỗi xảy ra khi tải thêm dữ liệu.');
                        },
                        complete: function() {
                            $btn.removeClass('loading').text('Tải thêm');
                        }
                    });
                });
            }
        },

        /**
         * Initialize animations
         */
        initAnimations: function() {
            // Animate cards on scroll
            if (typeof IntersectionObserver !== 'undefined') {
                var observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            $(entry.target).addClass('animate-in');
                        }
                    });
                }, {
                    threshold: 0.1
                });

                $('.nganh-hoc-card:not(.animate-in)').each(function() {
                    observer.observe(this);
                });
            }

            // Hover effects
            $('.nganh-hoc-card').hover(
                function() {
                    $(this).addClass('hovered');
                },
                function() {
                    $(this).removeClass('hovered');
                }
            );
        },

        /**
         * Initialize tooltips
         */
        initTooltips: function() {
            // Add tooltips to icons and buttons
            $('[data-tooltip]').each(function() {
                var $element = $(this);
                var tooltipText = $element.data('tooltip');
                
                $element.hover(
                    function() {
                        var $tooltip = $('<div class="tooltip">' + tooltipText + '</div>');
                        $('body').append($tooltip);
                        
                        var offset = $element.offset();
                        $tooltip.css({
                            top: offset.top - $tooltip.outerHeight() - 10,
                            left: offset.left + ($element.outerWidth() / 2) - ($tooltip.outerWidth() / 2)
                        }).fadeIn(200);
                    },
                    function() {
                        $('.tooltip').fadeOut(200, function() {
                            $(this).remove();
                        });
                    }
                );
            });
        },

        /**
         * Update results count
         */
        updateResultsCount: function() {
            var visibleCards = $('.nganh-hoc-card:visible').length;
            var totalCards = $('.nganh-hoc-card').length;
            
            var $counter = $('.results-count');
            if ($counter.length) {
                $counter.text('Hiển thị ' + visibleCards + ' trong tổng số ' + totalCards + ' ngành học');
            }
        },

        /**
         * Smooth scroll to element
         */
        scrollTo: function(target, offset) {
            offset = offset || 0;
            $('html, body').animate({
                scrollTop: $(target).offset().top - offset
            }, 800);
        }
    };

    // Initialize when document is ready
    NganhHoc.init();

    // Expose NganhHoc object globally
    window.NganhHoc = NganhHoc;

    // Handle single page functionality
    if ($('body').hasClass('single-nganh_hoc')) {
        
        // Smooth scroll for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            var target = $(this.getAttribute('href'));
            if (target.length) {
                NganhHoc.scrollTo(target, 100);
            }
        });

        // Print functionality
        $('.btn-print').on('click', function(e) {
            e.preventDefault();
            window.print();
        });

        // Share functionality
        $('.btn-share').on('click', function(e) {
            e.preventDefault();
            
            if (navigator.share) {
                navigator.share({
                    title: document.title,
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                var url = window.location.href;
                navigator.clipboard.writeText(url).then(function() {
                    alert('Đã sao chép link vào clipboard!');
                });
            }
        });

        // Back to top button
        var $backToTop = $('.back-to-top');
        if ($backToTop.length) {
            $(window).scroll(function() {
                if ($(this).scrollTop() > 300) {
                    $backToTop.fadeIn();
                } else {
                    $backToTop.fadeOut();
                }
            });

            $backToTop.on('click', function(e) {
                e.preventDefault();
                NganhHoc.scrollTo('body', 0);
            });
        }
    }

    // Add CSS animations
    var style = document.createElement('style');
    style.textContent = `
        .nganh-hoc-card {
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.6s ease;
        }
        
        .nganh-hoc-card.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        .tooltip {
            position: absolute;
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            white-space: nowrap;
            display: none;
        }
        
        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #667eea;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .back-to-top:hover {
            background: #5a6fd8;
            transform: translateY(-3px);
        }
    `;
    document.head.appendChild(style);
});
