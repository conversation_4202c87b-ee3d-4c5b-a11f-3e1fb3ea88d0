jQuery(document).ready(function($) {
    'use strict';

    // Initialize sortable for existing repeater items
    initSortable();

    // Add new item functionality
    $(document).on('click', '.add-item', function(e) {
        e.preventDefault();
        
        var target = $(this).data('target');
        var container = $('#' + target + '-repeater .repeater-items');
        var newIndex = container.find('.repeater-item').length;
        var newItem = '';

        if (target === 'application-requirements') {
            newItem = createRequirementItem(newIndex);
        } else if (target === 'application-process') {
            newItem = createProcessItem(newIndex);
        }

        container.append(newItem);
        updateIndices(container);
        initSortable();
    });

    // Remove item functionality
    $(document).on('click', '.remove-item', function(e) {
        e.preventDefault();
        
        var item = $(this).closest('.repeater-item');
        var container = item.closest('.repeater-items');
        
        item.fadeOut(300, function() {
            $(this).remove();
            updateIndices(container);
        });
    });

    // Create new requirement item HTML
    function createRequirementItem(index) {
        return `
            <div class="repeater-item" data-index="${index}">
                <div class="repeater-content">
                    <input type="text"
                           name="application_requirements[${index}][title]"
                           placeholder="Tiêu đề yêu cầu..."
                           value=""
                           required />
                    <textarea name="application_requirements[${index}][description]"
                            placeholder="Mô tả chi tiết yêu cầu..."
                            rows="3"
                            required></textarea>
                </div>
                <div class="repeater-actions">
                    <button type="button" class="button remove-item">
                        <span class="dashicons dashicons-trash"></span> Xóa
                    </button>
                    <span class="sort-handle" title="Kéo để sắp xếp">⋮⋮</span>
                </div>
            </div>
        `;
    }

    // Create new process step item HTML
    function createProcessItem(index) {
        return `
            <div class="repeater-item" data-index="${index}">
                <div class="repeater-content">
                    <input type="text"
                           name="application_process[${index}][title]"
                           placeholder="Tiêu đề bước..."
                           value=""
                           required />
                    <textarea name="application_process[${index}][description]"
                            placeholder="Mô tả chi tiết bước..."
                            rows="3"
                            required></textarea>
                </div>
                <div class="repeater-actions">
                    <button type="button" class="button remove-item">
                        <span class="dashicons dashicons-trash"></span> Xóa
                    </button>
                    <span class="sort-handle" title="Kéo để sắp xếp">⋮⋮</span>
                </div>
            </div>
        `;
    }

    // Initialize sortable functionality
    function initSortable() {
        $('.repeater-items').sortable({
            handle: '.sort-handle',
            placeholder: 'repeater-placeholder',
            axis: 'y',
            opacity: 0.8,
            cursor: 'move',
            update: function(event, ui) {
                updateIndices($(this));
            }
        });
    }

    // Update indices after sorting or adding/removing items
    function updateIndices(container) {
        container.find('.repeater-item').each(function(index) {
            $(this).attr('data-index', index);

            // Update input names for requirements (new structure)
            $(this).find('input[name*="application_requirements"][name*="[title]"]').attr('name', 'application_requirements[' + index + '][title]');
            $(this).find('textarea[name*="application_requirements"][name*="[description]"]').attr('name', 'application_requirements[' + index + '][description]');

            // Update input names for process steps
            $(this).find('input[name*="application_process"][name*="[title]"]').attr('name', 'application_process[' + index + '][title]');
            $(this).find('textarea[name*="application_process"][name*="[description]"]').attr('name', 'application_process[' + index + '][description]');
        });
    }

    // Add visual feedback for drag and drop
    $('.repeater-items').on('sortstart', function(event, ui) {
        ui.placeholder.height(ui.item.height());
        ui.placeholder.addClass('repeater-placeholder-active');
    });

    $('.repeater-items').on('sortstop', function(event, ui) {
        $('.repeater-placeholder-active').removeClass('repeater-placeholder-active');
    });

    // Auto-resize textareas
    $(document).on('input', '.repeater-content textarea', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Initialize existing textareas
    $('.repeater-content textarea').each(function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    // Confirm before removing items with content
    $(document).on('click', '.remove-item', function(e) {
        var item = $(this).closest('.repeater-item');
        var hasContent = false;

        // Check if item has any content
        item.find('input, textarea').each(function() {
            if ($(this).val().trim() !== '') {
                hasContent = true;
                return false;
            }
        });

        if (hasContent) {
            if (!confirm('Bạn có chắc chắn muốn xóa mục này? Dữ liệu sẽ bị mất.')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }
        }
    });

    // Form validation before submit
    $('form#post').on('submit', function(e) {
        var hasErrors = false;
        var errorMessages = [];

        // Validate requirements (new structure)
        $('#application-requirements-repeater .repeater-item').each(function(index) {
            var title = $(this).find('input[type="text"]');
            var description = $(this).find('textarea');

            if (title.val().trim() === '') {
                title.addClass('error');
                hasErrors = true;
                errorMessages.push('Tiêu đề yêu cầu #' + (index + 1) + ' không được để trống.');
            } else {
                title.removeClass('error');
            }

            if (description.val().trim() === '') {
                description.addClass('error');
                hasErrors = true;
                errorMessages.push('Mô tả yêu cầu #' + (index + 1) + ' không được để trống.');
            } else {
                description.removeClass('error');
            }
        });

        // Validate process steps
        $('#application-process-repeater .repeater-item').each(function(index) {
            var title = $(this).find('input[type="text"]');
            var description = $(this).find('textarea');

            if (title.val().trim() === '') {
                title.addClass('error');
                hasErrors = true;
                errorMessages.push('Tiêu đề bước #' + (index + 1) + ' không được để trống.');
            } else {
                title.removeClass('error');
            }

            if (description.val().trim() === '') {
                description.addClass('error');
                hasErrors = true;
                errorMessages.push('Mô tả bước #' + (index + 1) + ' không được để trống.');
            } else {
                description.removeClass('error');
            }
        });

        if (hasErrors) {
            e.preventDefault();
            alert('Vui lòng kiểm tra lại:\n\n' + errorMessages.join('\n'));
            return false;
        }
    });

    // Real-time validation
    $(document).on('blur', '.repeater-content input, .repeater-content textarea', function() {
        var $this = $(this);
        if ($this.val().trim() === '') {
            $this.addClass('error');
        } else {
            $this.removeClass('error');
        }
    });

    // Character counter for textareas
    $(document).on('input', '.repeater-content textarea', function() {
        var $this = $(this);
        var maxLength = 500; // Set max length
        var currentLength = $this.val().length;

        // Remove existing counter
        $this.siblings('.char-counter').remove();

        // Add counter
        var counterClass = currentLength > maxLength ? 'char-counter over-limit' : 'char-counter';
        var counter = $('<div class="' + counterClass + '">' + currentLength + '/' + maxLength + '</div>');
        $this.after(counter);

        if (currentLength > maxLength) {
            $this.addClass('over-limit');
        } else {
            $this.removeClass('over-limit');
        }
    });

    // Initialize character counters for existing textareas
    $('.repeater-content textarea').trigger('input');
});
