/**
 * Mega Menu Admin JavaScript
 * 
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    const MegaMenuAdmin = {
        
        init: function() {
            this.bindEvents();
        },
        
        bindEvents: function() {
            // Image upload functionality
            $(document).on('click', '.mega-menu-image-upload', this.handleImageUpload);
            
            // Toggle mega menu options
            $(document).on('change', '[id^="edit-menu-item-mega-menu-enabled-"]', this.toggleMegaMenuOptions);
            
            // Preview image when URL is entered
            $(document).on('blur', '[id^="edit-menu-item-mega-menu-image-"]', this.previewImage);
        },
        
        handleImageUpload: function(e) {
            e.preventDefault();
            
            const $button = $(this);
            const itemId = $button.data('item-id');
            const $imageField = $('#edit-menu-item-mega-menu-image-' + itemId);
            
            // Create media uploader
            const mediaUploader = wp.media({
                title: 'Select Mega Menu Image',
                button: {
                    text: 'Use This Image'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });
            
            // When image is selected
            mediaUploader.on('select', function() {
                const attachment = mediaUploader.state().get('selection').first().toJSON();
                
                // Set image URL
                $imageField.val(attachment.url);
                
                // Update preview
                MegaMenuAdmin.updateImagePreview(itemId, attachment.url);
            });
            
            // Open media uploader
            mediaUploader.open();
        },
        
        toggleMegaMenuOptions: function() {
            const $checkbox = $(this);
            const $options = $checkbox.closest('.mega-menu-fields').find('.mega-menu-options');
            
            if ($checkbox.is(':checked')) {
                $options.slideDown(200);
            } else {
                $options.slideUp(200);
            }
        },
        
        previewImage: function() {
            const $field = $(this);
            const imageUrl = $field.val();
            const itemId = $field.attr('id').replace('edit-menu-item-mega-menu-image-', '');
            
            if (imageUrl && MegaMenuAdmin.isValidImageUrl(imageUrl)) {
                MegaMenuAdmin.updateImagePreview(itemId, imageUrl);
            }
        },
        
        updateImagePreview: function(itemId, imageUrl) {
            const $field = $('#edit-menu-item-mega-menu-image-' + itemId);
            let $preview = $field.siblings('.mega-menu-image-preview');
            
            if ($preview.length === 0) {
                $preview = $('<div class="mega-menu-image-preview" style="margin-top: 10px;"></div>');
                $field.after($preview);
            }
            
            if (imageUrl) {
                $preview.html('<img src="' + imageUrl + '" style="max-width: 100px; height: auto;" />');
            } else {
                $preview.empty();
            }
        },
        
        isValidImageUrl: function(url) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
            const extension = url.split('.').pop().toLowerCase();
            return imageExtensions.includes(extension);
        }
    };

    // Initialize when document is ready
    $(document).ready(function() {
        MegaMenuAdmin.init();
    });

})(jQuery);
