/**
 * Mega Menu JavaScript for IntHub Theme
 * 
 * @package IntHub
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Mega Menu Object
    const MegaMenu = {
        
        // Configuration
        config: {
            hoverDelay: 150,
            animationDuration: 300,
            mobileBreakpoint: 768,
            touchDevice: 'ontouchstart' in window || navigator.maxTouchPoints > 0
        },

        // Initialize mega menu
        init: function() {
            this.bindEvents();
            this.setupAccessibility();
            this.handleResize();
        },

        // Bind all events
        bindEvents: function() {
            const self = this;
            
            // Desktop hover events
            $('.has-mega-menu').on('mouseenter', function() {
                if ($(window).width() >= self.config.mobileBreakpoint) {
                    self.showMegaMenu($(this));
                }
            }).on('mouseleave', function() {
                if ($(window).width() >= self.config.mobileBreakpoint) {
                    self.hideMegaMenu($(this));
                }
            });

            // Mobile click events
            $('.has-mega-menu > a').on('click', function(e) {
                if ($(window).width() < self.config.mobileBreakpoint) {
                    e.preventDefault();
                    const $parent = $(this).parent();
                    self.toggleMegaMenu($parent);
                }
            });

            // Close mega menu when clicking outside
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.has-mega-menu').length) {
                    self.hideAllMegaMenus();
                }
            });

            // Keyboard navigation
            $('.has-mega-menu > a').on('keydown', function(e) {
                self.handleKeyboardNavigation(e, $(this));
            });

            // Window resize handler
            $(window).on('resize', $.throttle(250, function() {
                self.handleResize();
            }));

            // Touch events for better mobile experience
            if (self.config.touchDevice) {
                $('.mega-menu-item').on('touchstart', function() {
                    $(this).addClass('touch-active');
                }).on('touchend', function() {
                    $(this).removeClass('touch-active');
                });
            }
        },

        // Show mega menu with animation
        showMegaMenu: function($menuItem) {
            const self = this;
            const $megaMenu = $menuItem.find('.mega-menu');
            
            if ($megaMenu.length === 0) return;

            // Clear any existing timeouts
            clearTimeout($menuItem.data('hideTimeout'));
            
            // Set show timeout for smooth interaction
            const showTimeout = setTimeout(function() {
                // Hide other mega menus first
                self.hideAllMegaMenus();
                
                // Show current mega menu
                $menuItem.addClass('active');
                $megaMenu.addClass('show');
                
                // Trigger stagger animation for items
                self.animateMenuItems($megaMenu);
                
                // Update ARIA attributes
                $menuItem.find('> a').attr('aria-expanded', 'true');
                $megaMenu.attr('aria-hidden', 'false');
                
            }, self.config.hoverDelay);
            
            $menuItem.data('showTimeout', showTimeout);
        },

        // Hide mega menu with animation
        hideMegaMenu: function($menuItem) {
            const self = this;
            const $megaMenu = $menuItem.find('.mega-menu');
            
            if ($megaMenu.length === 0) return;

            // Clear show timeout
            clearTimeout($menuItem.data('showTimeout'));
            
            // Set hide timeout
            const hideTimeout = setTimeout(function() {
                $menuItem.removeClass('active');
                $megaMenu.removeClass('show');
                
                // Update ARIA attributes
                $menuItem.find('> a').attr('aria-expanded', 'false');
                $megaMenu.attr('aria-hidden', 'true');
                
            }, self.config.hoverDelay);
            
            $menuItem.data('hideTimeout', hideTimeout);
        },

        // Toggle mega menu for mobile
        toggleMegaMenu: function($menuItem) {
            const $megaMenu = $menuItem.find('.mega-menu');
            
            if ($menuItem.hasClass('active')) {
                this.hideMegaMenu($menuItem);
            } else {
                this.hideAllMegaMenus();
                this.showMegaMenu($menuItem);
            }
        },

        // Hide all mega menus
        hideAllMegaMenus: function() {
            $('.has-mega-menu').each((index, element) => {
                this.hideMegaMenu($(element));
            });
        },

        // Animate menu items with stagger effect
        animateMenuItems: function($megaMenu) {
            const $items = $megaMenu.find('.mega-menu-item');
            
            $items.each(function(index) {
                const $item = $(this);
                const delay = index * 100;
                
                setTimeout(function() {
                    $item.addClass('animate-in');
                }, delay);
            });
        },

        // Handle keyboard navigation
        handleKeyboardNavigation: function(e, $link) {
            const $menuItem = $link.parent();
            
            switch(e.keyCode) {
                case 13: // Enter
                case 32: // Space
                    e.preventDefault();
                    this.toggleMegaMenu($menuItem);
                    break;
                    
                case 27: // Escape
                    this.hideMegaMenu($menuItem);
                    $link.focus();
                    break;
                    
                case 37: // Left Arrow
                    e.preventDefault();
                    this.focusPreviousMenuItem($menuItem);
                    break;
                    
                case 39: // Right Arrow
                    e.preventDefault();
                    this.focusNextMenuItem($menuItem);
                    break;
                    
                case 40: // Down Arrow
                    e.preventDefault();
                    if ($menuItem.hasClass('has-mega-menu')) {
                        this.showMegaMenu($menuItem);
                        this.focusFirstMegaMenuItem($menuItem);
                    }
                    break;
            }
        },

        // Focus previous menu item
        focusPreviousMenuItem: function($currentItem) {
            const $prevItem = $currentItem.prev('.menu-item');
            if ($prevItem.length) {
                $prevItem.find('> a').focus();
            }
        },

        // Focus next menu item
        focusNextMenuItem: function($currentItem) {
            const $nextItem = $currentItem.next('.menu-item');
            if ($nextItem.length) {
                $nextItem.find('> a').focus();
            }
        },

        // Focus first item in mega menu
        focusFirstMegaMenuItem: function($menuItem) {
            const $firstLink = $menuItem.find('.mega-menu a').first();
            if ($firstLink.length) {
                $firstLink.focus();
            }
        },

        // Setup accessibility attributes
        setupAccessibility: function() {
            $('.has-mega-menu').each(function() {
                const $menuItem = $(this);
                const $link = $menuItem.find('> a');
                const $megaMenu = $menuItem.find('.mega-menu');
                
                // Add ARIA attributes
                $link.attr({
                    'aria-expanded': 'false',
                    'aria-haspopup': 'true'
                });
                
                $megaMenu.attr({
                    'aria-hidden': 'true',
                    'role': 'menu'
                });
                
                // Add role to menu items
                $megaMenu.find('.mega-menu-links a').attr('role', 'menuitem');
            });
        },

        // Handle window resize
        handleResize: function() {
            const self = this;
            
            // Hide all mega menus on resize
            this.hideAllMegaMenus();
            
            // Reset mobile states
            if ($(window).width() >= self.config.mobileBreakpoint) {
                $('.has-mega-menu').removeClass('mobile-active');
            }
        },

        // Utility: Check if device is mobile
        isMobile: function() {
            return $(window).width() < this.config.mobileBreakpoint;
        },

        // Utility: Throttle function
        throttle: function(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }
    };

    // jQuery throttle plugin (if not already available)
    if (!$.throttle) {
        $.throttle = function(delay, fn) {
            let timer = null;
            return function() {
                const context = this, args = arguments;
                clearTimeout(timer);
                timer = setTimeout(function() {
                    fn.apply(context, args);
                }, delay);
            };
        };
    }

    // Initialize when document is ready
    $(document).ready(function() {
        MegaMenu.init();
    });

    // Expose MegaMenu object globally
    window.IntHubMegaMenu = MegaMenu;

})(jQuery);
