<?php
/**
 * IntHub Theme Functions
 * 
 * This file serves as the main entry point for all theme functionality.
 * All specific features are organized into separate modules for better maintainability.
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Define theme constants
 */
define('INTHUB_THEME_VERSION', '1.0.0');
define('INTHUB_THEME_PATH', get_template_directory());
define('INTHUB_THEME_URL', get_template_directory_uri());
define('INTHUB_INC_PATH', INTHUB_THEME_PATH . '/inc');

/**
 * Core Theme Setup and Configuration
 */
require_once INTHUB_INC_PATH . '/theme-setup.php';

/**
 * Scripts and Styles Enqueuing
 */
require_once INTHUB_INC_PATH . '/enqueue-scripts.php';

/**
 * Widget Areas and Custom Widgets
 */
require_once INTHUB_INC_PATH . '/widgets.php';

/**
 * Custom Post Types
 */
require_once INTHUB_INC_PATH . '/post-types/testimonials.php';
require_once INTHUB_INC_PATH . '/post-types/services.php';
require_once INTHUB_INC_PATH . '/post-types/events.php';
require_once INTHUB_INC_PATH . '/post-types/universities.php';
require_once INTHUB_INC_PATH . '/post-types/scholarships.php';
require_once INTHUB_INC_PATH . '/post-types/nganh-hoc.php';

/**
 * Custom Taxonomies
 */
require_once INTHUB_INC_PATH . '/taxonomies/university-categories.php';
require_once INTHUB_INC_PATH . '/taxonomies/scholarship-categories.php';
require_once INTHUB_INC_PATH . '/taxonomies/nganh-hoc-taxonomies.php';

/**
 * Meta Boxes
 */
require_once INTHUB_INC_PATH . '/meta-boxes/event-meta.php';
require_once INTHUB_INC_PATH . '/meta-boxes/university-meta.php';
require_once INTHUB_INC_PATH . '/meta-boxes/scholarship-meta.php';
require_once INTHUB_INC_PATH . '/meta-boxes/nganh-hoc-meta.php';

/**
 * Admin Customizations
 */
require_once INTHUB_INC_PATH . '/admin/admin-columns.php';
require_once INTHUB_INC_PATH . '/admin/admin-filters.php';
require_once INTHUB_INC_PATH . '/admin/dashboard-widgets.php';

/**
 * AJAX Functions
 */
require_once INTHUB_INC_PATH . '/ajax/load-more-universities.php';
require_once INTHUB_INC_PATH . '/ajax/load-more-scholarships.php';

/**
 * Helper Functions
 */
require_once INTHUB_INC_PATH . '/helpers/university-helpers.php';
require_once INTHUB_INC_PATH . '/helpers/scholarship-helpers.php';
require_once INTHUB_INC_PATH . '/helpers/general-helpers.php';

/**
 * Theme Customizer
 */
require_once INTHUB_INC_PATH . '/customizer.php';

/**
 * Navigation and Menu Functions
 */
require_once INTHUB_INC_PATH . '/navigation.php';

/**
 * SEO and Meta Functions
 */
require_once INTHUB_INC_PATH . '/seo.php';

/**
 * Security Functions (will be created in next steps)
 */
// require_once INTHUB_INC_PATH . '/security.php';

/**
 * Performance Optimizations (will be created in next steps)
 */
// require_once INTHUB_INC_PATH . '/performance.php';

/**
 * Compatibility function to ensure all post types and taxonomies are registered
 * This function is called by theme-setup.php during theme activation
 */
function inthub_custom_post_types() {
    // Call all post type registration functions
    if (function_exists('inthub_register_testimonials_post_type')) {
        inthub_register_testimonials_post_type();
    }
    if (function_exists('inthub_register_services_post_type')) {
        inthub_register_services_post_type();
    }
    if (function_exists('inthub_register_events_post_type')) {
        inthub_register_events_post_type();
    }
    if (function_exists('inthub_register_universities_post_type')) {
        inthub_register_universities_post_type();
    }
    if (function_exists('inthub_register_scholarships_post_type')) {
        inthub_register_scholarships_post_type();
    }
    if (function_exists('inthub_register_nganh_hoc_post_type')) {
        inthub_register_nganh_hoc_post_type();
    }
}

/**
 * Compatibility function to ensure all taxonomies are registered
 * This function is called by theme-setup.php during theme activation
 */
function inthub_custom_taxonomies() {
    // Call all taxonomy registration functions
    if (function_exists('inthub_register_university_category_taxonomy')) {
        inthub_register_university_category_taxonomy();
    }
    if (function_exists('inthub_register_scholarship_category_taxonomy')) {
        inthub_register_scholarship_category_taxonomy();
    }
    if (function_exists('inthub_register_nganh_hoc_taxonomies')) {
        inthub_register_nganh_hoc_taxonomies();
    }
}

/**
 * Theme activation hook
 */
function inthub_theme_activation() {
    // Ensure all post types and taxonomies are registered
    inthub_custom_post_types();
    inthub_custom_taxonomies();
    
    // Flush rewrite rules
    flush_rewrite_rules();
}
register_activation_hook(__FILE__, 'inthub_theme_activation');

/**
 * Theme deactivation hook
 */
function inthub_theme_deactivation() {
    // Flush rewrite rules
    flush_rewrite_rules();
}
register_deactivation_hook(__FILE__, 'inthub_theme_deactivation');
