# Hướng Dẫn Sử Dụng Scholarship Taxonomy

## Tổng Quan

Hệ thống taxonomy mới cho custom post type "scholarship" đã được triển khai với đầy đủ tính năng:

- **Taxonomy Name**: `scholarship_category`
- **Hierarchical**: <PERSON><PERSON> (hỗ trợ danh mục con)
- **Public**: <PERSON><PERSON> (hiển thị trên frontend)
- **SEO Friendly**: URLs thân thiện và meta tags

## 1. C<PERSON>u Trúc <PERSON>h Mục Mặc Định

### Theo Loại Học Bổng
- **Học bổng toàn phần** (`hoc-bong-toan-phan`)
- **Học bổng bán phần** (`hoc-bong-ban-phan`)
- **Học bổng nghiên cứu** (`hoc-bong-nghien-cuu`)
- **Học bổng thể thao** (`hoc-bong-the-thao`)
- **Học bổng nghệ thuật** (`hoc-bong-nghe-thuat`)

### <PERSON>
- **<PERSON><PERSON><PERSON> họ<PERSON>** (`dai-hoc`)
- **Th<PERSON><PERSON> s<PERSON>** (`thac-si`)
- **Tiến sĩ** (`tien-si`)
- **Sau tiến sĩ** (`sau-tien-si`)

### Theo Lĩnh Vực
- **Khoa học tự nhiên** (`khoa-hoc-tu-nhien`)
- **Công nghệ thông tin** (`cong-nghe-thong-tin`)
- **Kinh tế** (`kinh-te`)
- **Y khoa** (`y-khoa`)
- **Kỹ thuật** (`ky-thuat`)
- **Nghệ thuật** (`nghe-thuat`)
- **Khoa học xã hội** (`khoa-hoc-xa-hoi`)

### Theo Nguồn Tài Trợ
- **Chính phủ** (`chinh-phu`)
- **Trường đại học** (`truong-dai-hoc`)
- **Tổ chức tư nhân** (`to-chuc-tu-nhan`)
- **Quỹ quốc tế** (`quy-quoc-te`)

## 2. Cách Sử Dụng Trong Admin

### Quản Lý Danh Mục

1. **Truy cập**: Admin → Học Bổng → Danh mục học bổng
2. **Thêm danh mục mới**:
   - Nhập tên danh mục
   - Chọn danh mục cha (nếu có)
   - Nhập mô tả
   - Slug sẽ tự động tạo

3. **Chỉnh sửa danh mục**:
   - Click vào tên danh mục để chỉnh sửa
   - Cập nhật thông tin cần thiết
   - Lưu thay đổi

### Gán Danh Mục Cho Scholarship

1. **Khi tạo/sửa scholarship**:
   - Tìm meta box "Danh mục học bổng" ở sidebar
   - Check vào các danh mục phù hợp
   - Có thể chọn nhiều danh mục
   - Lưu bài viết

2. **Bulk edit**:
   - Chọn nhiều scholarships
   - Chọn "Bulk Actions" → "Edit"
   - Thêm/xóa danh mục hàng loạt

## 3. Hiển Thị Trên Frontend

### Single Scholarship Page

**Vị trí**: Hiển thị trong hero section
**Styling**: Tags màu hồng với background trong suốt
**Code example**:
```php
$categories = inthub_get_scholarship_categories();
if ($categories) {
    foreach ($categories as $category) {
        echo '<span class="category-tag">' . $category->name . '</span>';
    }
}
```

### Archive Scholarship Page

**Vị trí**: 
- Filter dropdown trong phần filter
- Tags nhỏ trong mỗi scholarship card

**Chức năng**:
- Filter theo danh mục
- Hiển thị danh mục của từng học bổng

### Taxonomy Archive Page

**URL**: `/danh-muc-hoc-bong/{category-slug}/`
**Template**: `taxonomy-scholarship_category.php`
**Features**:
- Hero section với thông tin danh mục
- Breadcrumb navigation
- Filter và search
- Grid hiển thị scholarships
- Pagination

## 4. Helper Functions

### Lấy Danh Mục

```php
// Lấy tất cả danh mục của scholarship
$categories = inthub_get_scholarship_categories($post_id);

// Lấy chỉ tên danh mục
$category_names = inthub_get_scholarship_categories($post_id, 'names');

// Lấy chỉ slug danh mục
$category_slugs = inthub_get_scholarship_categories($post_id, 'slugs');
```

### Hiển Thị Danh Mục

```php
// Hiển thị với links
echo inthub_display_scholarship_categories($post_id, 'custom-class', true);

// Hiển thị không có links
echo inthub_display_scholarship_categories($post_id, 'custom-class', false);
```

### Lấy Scholarships Theo Danh Mục

```php
// Lấy tất cả scholarships trong danh mục
$scholarships = inthub_get_scholarships_by_category('hoc-bong-toan-phan');

// Với custom args
$scholarships = inthub_get_scholarships_by_category('dai-hoc', array(
    'posts_per_page' => 10,
    'meta_key' => 'scholarship_value',
    'orderby' => 'meta_value'
));
```

## 5. SEO Features

### URLs Thân Thiện
- **Archive**: `/danh-muc-hoc-bong/hoc-bong-toan-phan/`
- **Hierarchical**: `/danh-muc-hoc-bong/cap-do/dai-hoc/`

### Meta Tags
- **Title**: Tự động tạo từ tên danh mục
- **Description**: Sử dụng mô tả danh mục hoặc tự động tạo
- **Open Graph**: Đầy đủ OG tags
- **Twitter Card**: Hỗ trợ Twitter Cards
- **Canonical**: URL canonical chính xác

### Sitemap
- Tự động thêm vào XML sitemap (nếu dùng Yoast SEO)
- Hỗ trợ Google indexing

## 6. Widget

### Scholarship Categories Widget

**Vị trí**: Appearance → Widgets
**Tên**: "Danh mục học bổng"
**Options**:
- Tiêu đề widget
- Hiển thị số lượng
- Hiển thị phân cấp

**Sử dụng**:
1. Kéo widget vào sidebar
2. Cấu hình options
3. Lưu widget

## 7. JavaScript Integration

### Filter Functionality

File: `assets/js/scholarship.js`

**Filter theo danh mục**:
```javascript
$('#category-filter').on('change', function() {
    var selectedCategory = $(this).val();
    // Filter logic here
});
```

**Data attributes**:
```html
<div class="scholarship-card" data-categories="hoc-bong-toan-phan,dai-hoc">
```

## 8. Styling và CSS

### Category Tags Styling

File: `assets/css/scholarship-taxonomy.css`

**Basic styling**:
```css
.scholarship-categories .category-link {
    background: rgba(250, 60, 128, 0.1);
    color: #fa3c80;
    padding: 4px 8px;
    border-radius: 4px;
}
```

**Category-specific colors**:
```css
.category-hoc-bong-toan-phan {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}
```

## 9. Customization

### Thay Đổi Slug

Trong `functions.php`, tìm:
```php
'rewrite' => array(
    'slug' => 'danh-muc-hoc-bong', // Thay đổi ở đây
    'with_front' => false,
    'hierarchical' => true,
),
```

### Thêm Custom Fields Cho Taxonomy

```php
// Hook vào taxonomy edit form
add_action('scholarship_category_edit_form_fields', 'add_custom_scholarship_taxonomy_fields');
add_action('edited_scholarship_category', 'save_custom_scholarship_taxonomy_fields');

function add_custom_scholarship_taxonomy_fields($term) {
    $icon = get_term_meta($term->term_id, 'category_icon', true);
    ?>
    <tr class="form-field">
        <th scope="row"><label for="category_icon">Icon</label></th>
        <td>
            <input type="text" name="category_icon" id="category_icon" value="<?php echo esc_attr($icon); ?>">
        </td>
    </tr>
    <?php
}
```

## 10. Performance Optimization

### Caching

```php
// Cache taxonomy queries
$categories = wp_cache_get('scholarship_categories_all', 'scholarship');
if (false === $categories) {
    $categories = get_terms('scholarship_category');
    wp_cache_set('scholarship_categories_all', $categories, 'scholarship', HOUR_IN_SECONDS);
}
```

### Database Optimization

- Sử dụng `hide_empty => true` khi không cần danh mục trống
- Limit số lượng terms khi query
- Sử dụng `fields => 'ids'` khi chỉ cần ID

## 11. Integration với Existing Features

### Với Custom Fields
- Tương thích với "Yêu cầu ứng tuyển" và "Quy trình ứng tuyển"
- Không ảnh hưởng đến các meta fields hiện có

### Với Search và Filter
- Tích hợp với hệ thống filter hiện có
- Hỗ trợ combined filtering

### Với University Relationship
- Hoạt động độc lập với university taxonomy
- Có thể filter theo cả university và scholarship category

## 12. Troubleshooting

### Taxonomy Không Hiển Thị
1. Kiểm tra user permissions
2. Đảm bảo `show_ui => true`
3. Clear cache nếu có
4. Flush rewrite rules

### Filter Không Hoạt Động
1. Kiểm tra JavaScript console
2. Đảm bảo data attributes đúng format
3. Kiểm tra jQuery loaded
4. Verify category slugs

### SEO Issues
1. Kiểm tra rewrite rules
2. Verify canonical URLs
3. Test meta tags
4. Check sitemap inclusion

## Support

Để được hỗ trợ:
1. Kiểm tra console errors
2. Verify database structure
3. Test với default theme
4. Cung cấp error logs nếu có
